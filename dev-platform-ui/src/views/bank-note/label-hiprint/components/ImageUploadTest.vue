<template>
  <div class="image-upload-test">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>图片上传功能测试</span>
        </div>
      </template>
      
      <div class="test-section">
        <h4>测试上传接口响应解析</h4>
        <el-button @click="testUploadResponse" type="primary">
          测试解析上传响应
        </el-button>
        
        <div v-if="testResult" class="test-result">
          <h5>解析结果：</h5>
          <pre>{{ JSON.stringify(testResult, null, 2) }}</pre>
        </div>
      </div>

      <div class="test-section">
        <h4>测试图片URL构建</h4>
        <el-input 
          v-model="testFileId" 
          placeholder="输入文件ID"
          style="width: 300px; margin-right: 10px;"
        />
        <el-button @click="testImageUrl" type="success">
          测试图片URL
        </el-button>
        
        <div v-if="constructedUrl" class="test-result">
          <h5>构建的URL：</h5>
          <p>{{ constructedUrl }}</p>
          <img 
            :src="constructedUrl" 
            alt="测试图片" 
            style="max-width: 200px; max-height: 200px; border: 1px solid #ddd;"
            @load="onImageLoad"
            @error="onImageError"
          />
        </div>
      </div>

      <div class="test-section">
        <h4>测试图片信息获取</h4>
        <el-button @click="testImageInfo" type="warning">
          测试获取图片信息
        </el-button>
        
        <div v-if="imageInfo" class="test-result">
          <h5>图片信息：</h5>
          <p>宽度: {{ imageInfo.width }}px</p>
          <p>高度: {{ imageInfo.height }}px</p>
          <p>大小: {{ imageInfo.size }}</p>
        </div>
      </div>

      <div class="test-section">
        <h4>完整上传测试</h4>
        <ImageUploadModal
          v-model="showModal"
          @confirm="handleImageConfirm"
        />
        <el-button @click="showModal = true" type="info">
          打开图片上传模态框
        </el-button>
        
        <div v-if="selectedImage" class="test-result">
          <h5>选择的图片：</h5>
          <p>URL: {{ selectedImage.url }}</p>
          <p>尺寸: {{ selectedImage.width }} × {{ selectedImage.height }}</p>
          <p>大小: {{ selectedImage.size }}</p>
          <img 
            :src="selectedImage.url" 
            alt="选择的图片" 
            style="max-width: 200px; max-height: 200px; border: 1px solid #ddd;"
          />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { ElMessage } from 'element-plus/es';
import ImageUploadModal from './ImageUploadModal.vue';
import { getImageDimensions, formatFileSize } from '@/utils/image-utils';

// 响应式数据
const testResult = ref(null);
const testFileId = ref('6893822dd39463bf55fe3ab3');
const constructedUrl = ref('');
const imageInfo = ref(null);
const showModal = ref(false);
const selectedImage = ref(null);

// 测试上传响应解析
const testUploadResponse = () => {
  // 模拟服务器返回的数据
  const mockResponse = {
    "code": 0,
    "message": "操作成功",
    "data": "[{\"contentType\":\"image/png\",\"id\":\"6893822dd39463bf55fe3ab3\",\"originalFilename\":\"5star.png\",\"size\":10411}]",
    "error": null
  };

  try {
    // 解析返回的数据
    const fileData = JSON.parse(mockResponse.data);
    console.log('解析后的文件数据:', fileData);
    
    if (Array.isArray(fileData) && fileData.length > 0) {
      const file = fileData[0];
      // 构建完整的图片访问URL
      const imageUrl = `${window.location.origin}/api/file/inline/${file.id}`;
      
      testResult.value = {
        originalResponse: mockResponse,
        parsedData: fileData,
        constructedUrl: imageUrl,
        fileInfo: file
      };
      
      ElMessage.success('响应解析成功');
    } else {
      ElMessage.error('数据格式错误');
    }
  } catch (error) {
    console.error('解析失败:', error);
    ElMessage.error('解析失败: ' + error.message);
  }
};

// 测试图片URL构建
const testImageUrl = () => {
  if (!testFileId.value) {
    ElMessage.error('请输入文件ID');
    return;
  }
  
  constructedUrl.value = `${window.location.origin}/api/file/inline/${testFileId.value}`;
  ElMessage.info('URL构建完成，请查看图片是否能正常加载');
};

// 图片加载成功
const onImageLoad = () => {
  ElMessage.success('图片加载成功');
};

// 图片加载失败
const onImageError = (error) => {
  console.error('图片加载失败:', error);
  ElMessage.error('图片加载失败，请检查文件ID是否正确');
};

// 测试图片信息获取
const testImageInfo = async () => {
  if (!constructedUrl.value) {
    ElMessage.error('请先构建图片URL');
    return;
  }
  
  try {
    const dimensions = await getImageDimensions(constructedUrl.value);
    
    // 尝试获取文件大小
    let size = '未知';
    try {
      const response = await fetch(constructedUrl.value, { method: 'HEAD' });
      const contentLength = response.headers.get('content-length');
      if (contentLength) {
        size = formatFileSize(parseInt(contentLength));
      }
    } catch (fetchError) {
      console.warn('无法获取文件大小:', fetchError);
    }
    
    imageInfo.value = {
      width: dimensions.width,
      height: dimensions.height,
      size: size
    };
    
    ElMessage.success('图片信息获取成功');
  } catch (error) {
    console.error('获取图片信息失败:', error);
    ElMessage.error('获取图片信息失败: ' + error.message);
  }
};

// 处理图片选择确认
const handleImageConfirm = (imageData) => {
  selectedImage.value = imageData;
  ElMessage.success('图片选择成功');
};
</script>

<style scoped>
.image-upload-test {
  padding: 20px;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.test-section h4 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #303133;
}

.test-result {
  margin-top: 15px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.test-result h5 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #606266;
}

.test-result pre {
  background-color: #fff;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.test-result p {
  margin: 5px 0;
  color: #606266;
}

.test-result img {
  margin-top: 10px;
  display: block;
}
</style>
