[2m2025-08-06 00:11:20.238[0;39m [32m INFO[0;39m [35m62220[0;39m [2m---[0;39m [2m[nexc.home:27017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Exception in monitor thread while connecting to server paynexc.home:27017

com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.internal.connection.InternalStreamConnection.createReadTimeoutException(InternalStreamConnection.java:819)
	at com.mongodb.internal.connection.InternalStreamConnection.translateReadException(InternalStreamConnection.java:807)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:857)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:517)
	at com.mongodb.internal.connection.InternalStreamConnection.receive(InternalStreamConnection.java:469)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitor.lookupServerDescription(DefaultServerMonitor.java:249)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitor.run(DefaultServerMonitor.java:176)
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.base/sun.nio.ch.NioSocketImpl.timedRead(NioSocketImpl.java:288)
	at java.base/sun.nio.ch.NioSocketImpl.implRead(NioSocketImpl.java:314)
	at java.base/sun.nio.ch.NioSocketImpl.read(NioSocketImpl.java:355)
	at java.base/sun.nio.ch.NioSocketImpl$1.read(NioSocketImpl.java:808)
	at java.base/java.net.Socket$SocketInputStream.read(Socket.java:976)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:182)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:824)
	... 4 common frames omitted

[2m2025-08-06 00:11:20.304[0;39m [32m INFO[0;39m [35m62220[0;39m [2m---[0;39m [2m[nexc.home:27017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Monitor thread successfully connected to server with description ServerDescription{address=paynexc.home:27017, type=STANDALONE, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=21, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=2887041, minRoundTripTimeNanos=0}
[2m2025-08-06 01:40:48.711[0;39m [31mERROR[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.a.security.JwtAuthenticationFilter  [0;39m [2m:[0;39m JWT expired at 2025-08-05T16:47:02Z. Current time: 2025-08-05T17:40:48Z, a difference of 3226682 milliseconds.  Allowed clock skew: 0 milliseconds.

io.jsonwebtoken.ExpiredJwtException: JWT expired at 2025-08-05T16:47:02Z. Current time: 2025-08-05T17:40:48Z, a difference of 3226682 milliseconds.  Allowed clock skew: 0 milliseconds.
	at io.jsonwebtoken.impl.DefaultJwtParser.parse(DefaultJwtParser.java:448)
	at io.jsonwebtoken.impl.DefaultJwtParser.parse(DefaultJwtParser.java:550)
	at io.jsonwebtoken.impl.DefaultJwtParser.parseClaimsJws(DefaultJwtParser.java:610)
	at io.jsonwebtoken.impl.ImmutableJwtParser.parseClaimsJws(ImmutableJwtParser.java:173)
	at com.payne.core.utils.JwtUtil.parseToken(JwtUtil.java:117)
	at com.payne.core.utils.JwtUtil.parseToken(JwtUtil.java:103)
	at com.payne.auth.security.JwtAuthenticationFilter.processUserToken(JwtAuthenticationFilter.java:143)
	at com.payne.auth.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:87)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:117)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:87)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.ServletRequestPathFilter.doFilter(ServletRequestPathFilter.java:52)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebSecurityConfiguration.java:319)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$4(HandlerMappingIntrospector.java:267)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:240)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:90)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-06 01:40:58.853[0;39m [31mERROR[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.core.handler.GlobalExceptionHandler [0;39m [2m:[0;39m 验证码错误或已过期

com.payne.core.exception.BusinessException: 验证码错误或已过期
	at com.payne.core.utils.AssertUtil.isTrue(AssertUtil.java:28)
	at com.payne.upms.system.controller.MainController.login(MainController.java:102)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:101)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:125)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:119)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at com.payne.auth.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:66)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:117)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:87)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.ServletRequestPathFilter.doFilter(ServletRequestPathFilter.java:52)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebSecurityConfiguration.java:319)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$4(HandlerMappingIntrospector.java:267)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:240)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:90)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-06 01:40:58.857[0;39m [33m WARN[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36m.m.m.a.ExceptionHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [BusinessException(code=1, data=null)]
[2m2025-08-06 01:41:06.319[0;39m [31mERROR[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.core.handler.GlobalExceptionHandler [0;39m [2m:[0;39m 验证码错误或已过期

com.payne.core.exception.BusinessException: 验证码错误或已过期
	at com.payne.core.utils.AssertUtil.isTrue(AssertUtil.java:28)
	at com.payne.upms.system.controller.MainController.login(MainController.java:102)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:101)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:125)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:119)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at com.payne.auth.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:66)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:117)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:87)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.ServletRequestPathFilter.doFilter(ServletRequestPathFilter.java:52)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebSecurityConfiguration.java:319)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$4(HandlerMappingIntrospector.java:267)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:240)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:90)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-06 01:41:06.321[0;39m [33m WARN[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36m.m.m.a.ExceptionHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [BusinessException(code=1, data=null)]
[2m2025-08-06 01:41:10.903[0;39m [32mDEBUG[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,ACCOUNT_NON_EXPIRED,ACCOUNT_NON_LOCKED,CREDENTIALS_NON_EXPIRED,ENABLED,PASSWORD,USERNAME,REAL_NAME,ONLINE_NAME,GENDER,ACTIVE_FLAG,ID_TYPE,ID_CODE,DEPT_NAME,TEL_MOBILE,EMAIL,START_TIME,END_TIME,PASSWORD_LAST_UPDATE_TIME    FROM  SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-06 01:41:10.923[0;39m [32mDEBUG[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,ACCOUNT_NON_EXPIRED,ACCOUNT_NON_LOCKED,CREDENTIALS_NON_EXPIRED,ENABLED,PASSWORD,USERNAME,REAL_NAME,ONLINE_NAME,GENDER,ACTIVE_FLAG,ID_TYPE,ID_CODE,DEPT_NAME,TEL_MOBILE,EMAIL,START_TIME,END_TIME,PASSWORD_LAST_UPDATE_TIME    FROM  SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-06 01:41:10.926[0;39m [32mDEBUG[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, ACCOUNT_NON_EXPIRED, ACCOUNT_NON_LOCKED, CREDENTIALS_NON_EXPIRED, ENABLED, PASSWORD, USERNAME, REAL_NAME, ONLINE_NAME, GENDER, ACTIVE_FLAG, ID_TYPE, ID_CODE, DEPT_NAME, TEL_MOBILE, EMAIL, START_TIME, END_TIME, PASSWORD_LAST_UPDATE_TIME FROM SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-06 01:41:11.056[0;39m [32mDEBUG[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, ACCOUNT_NON_EXPIRED, ACCOUNT_NON_LOCKED, CREDENTIALS_NON_EXPIRED, ENABLED, PASSWORD, USERNAME, REAL_NAME, ONLINE_NAME, GENDER, ACTIVE_FLAG, ID_TYPE, ID_CODE, DEPT_NAME, TEL_MOBILE, EMAIL, START_TIME, END_TIME, PASSWORD_LAST_UPDATE_TIME FROM SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-06 01:41:11.062[0;39m [32mDEBUG[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: admin(String), admin(String)
[2m2025-08-06 01:41:11.076[0;39m [32mDEBUG[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-06 01:41:11.080[0;39m [32mDEBUG[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT *
        FROM SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-06 01:41:11.162[0;39m [32mDEBUG[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT *
        FROM SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-06 01:41:11.185[0;39m [32mDEBUG[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT * FROM SYS_ROLE WHERE id IN (SELECT role_id FROM SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-06 01:41:11.185[0;39m [32mDEBUG[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM SYS_ROLE WHERE id IN (SELECT role_id FROM SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-06 01:41:11.185[0;39m [32mDEBUG[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==> Parameters: 13FF28E5AFA4D417E0630100007FE3FC(String)
[2m2025-08-06 01:41:11.192[0;39m [32mDEBUG[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-06 01:41:11.195[0;39m [32mDEBUG[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT a.*
        FROM SYS_MENU a
         WHERE  a.menu_id IN (
            SELECT menu_id FROM SYS_ROLE_MENU WHERE role_id IN (
            SELECT ta.role_id FROM SYS_ACCOUNT_ROLE ta LEFT JOIN sys_role tb ON ta.role_id = tb.id
            WHERE ta.account_id = ? and ta.role_id = ?
            )
            )
            
            AND a.deleted = 0 
        ORDER BY a.sort_number
[2m2025-08-06 01:41:11.242[0;39m [32mDEBUG[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT a.*
        FROM SYS_MENU a
         WHERE  a.menu_id IN (
            SELECT menu_id FROM SYS_ROLE_MENU WHERE role_id IN (
            SELECT ta.role_id FROM SYS_ACCOUNT_ROLE ta LEFT JOIN sys_role tb ON ta.role_id = tb.id
            WHERE ta.account_id = ? and ta.role_id = ?
            )
            )
            
            AND a.deleted = 0 
        ORDER BY a.sort_number
[2m2025-08-06 01:41:11.262[0;39m [32mDEBUG[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT a.* FROM SYS_MENU a WHERE a.menu_id IN (SELECT menu_id FROM SYS_ROLE_MENU WHERE role_id IN (SELECT ta.role_id FROM SYS_ACCOUNT_ROLE ta LEFT JOIN sys_role tb ON ta.role_id = tb.id WHERE ta.account_id = ? AND ta.role_id = ?)) AND a.deleted = 0 ORDER BY a.sort_number
[2m2025-08-06 01:41:11.262[0;39m [32mDEBUG[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.R.listMenuByAccount           [0;39m [2m:[0;39m ==>  Preparing: SELECT a.* FROM SYS_MENU a WHERE a.menu_id IN (SELECT menu_id FROM SYS_ROLE_MENU WHERE role_id IN (SELECT ta.role_id FROM SYS_ACCOUNT_ROLE ta LEFT JOIN sys_role tb ON ta.role_id = tb.id WHERE ta.account_id = ? AND ta.role_id = ?)) AND a.deleted = 0 ORDER BY a.sort_number
[2m2025-08-06 01:41:11.263[0;39m [32mDEBUG[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.R.listMenuByAccount           [0;39m [2m:[0;39m ==> Parameters: 13FF28E5AFA4D417E0630100007FE3FC(String), 36d5ca2b3906a97f4274ffb8e8a36210(String)
[2m2025-08-06 01:41:11.296[0;39m [32mDEBUG[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.R.listMenuByAccount           [0;39m [2m:[0;39m <==      Total: 71
[2m2025-08-06 01:41:11.690[0;39m [32mDEBUG[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM
[2m2025-08-06 01:41:11.731[0;39m [32mDEBUG[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM
[2m2025-08-06 01:41:11.741[0;39m [32mDEBUG[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM
[2m2025-08-06 01:41:11.758[0;39m [32mDEBUG[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM PJ_O_SENDFORM
[2m2025-08-06 01:41:11.759[0;39m [32mDEBUG[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-06 01:41:11.770[0;39m [32mDEBUG[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-06 01:41:11.808[0;39m [32mDEBUG[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM ORDER BY inupttime DESC ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-08-06 01:41:11.809[0;39m [32mDEBUG[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: 10(Long), 0(Long)
[2m2025-08-06 01:41:11.816[0;39m [32mDEBUG[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-06 01:41:21.766[0;39m [32mDEBUG[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-08-06 01:41:21.784[0;39m [32mDEBUG[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-08-06 01:41:21.791[0;39m [32mDEBUG[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-08-06 01:41:21.793[0;39m [32mDEBUG[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-08-06 01:41:21.793[0;39m [32mDEBUG[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-08-06 01:41:21.806[0;39m [32mDEBUG[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-06 01:41:21.819[0;39m [32mDEBUG[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,SEQNO,COIN_TYPE,COIN_YEAR AS year,FACE_VALUE,COIN_VERSION AS version,QUANTITY,GRADE_FEE,REMARK,DIY_CODE,SERIAL_NUMBER,COIN_NAME1,COIN_NAME2,COIN_NAME3,FACE_VAL,BELONG_NAME,WEIGHT_NAME,MATERIAL,BOX_TYPE,STANDARD_PRICE,INTERNATIONAL_PRICE,FEE,DISCOUNT,COIN_SIZE,COIN_WEIGHT,YEAR_INFO,REGION,TAX_TYPE,BOX_FEE,URGENT_FEE,CATALOG,RANK,BANK_NAME,SPECIAL_MARK,INTERNAL_NOTE,EXTERNAL_NOTE,SPECIAL_LABEL,AUTHENTICITY,GRADE_SCORE_VALUE,GRADE_SCORE,SCORE_REMARKS,INSPECTION_NOTE,COIN_IMAGES,CREATE_TIME    FROM  PJ_O_SENDFORM_ITEM         WHERE  (SENDNUM = ?) ORDER BY SEQNO ASC
[2m2025-08-06 01:41:21.836[0;39m [32mDEBUG[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,SEQNO,COIN_TYPE,COIN_YEAR AS year,FACE_VALUE,COIN_VERSION AS version,QUANTITY,GRADE_FEE,REMARK,DIY_CODE,SERIAL_NUMBER,COIN_NAME1,COIN_NAME2,COIN_NAME3,FACE_VAL,BELONG_NAME,WEIGHT_NAME,MATERIAL,BOX_TYPE,STANDARD_PRICE,INTERNATIONAL_PRICE,FEE,DISCOUNT,COIN_SIZE,COIN_WEIGHT,YEAR_INFO,REGION,TAX_TYPE,BOX_FEE,URGENT_FEE,CATALOG,RANK,BANK_NAME,SPECIAL_MARK,INTERNAL_NOTE,EXTERNAL_NOTE,SPECIAL_LABEL,AUTHENTICITY,GRADE_SCORE_VALUE,GRADE_SCORE,SCORE_REMARKS,INSPECTION_NOTE,COIN_IMAGES,CREATE_TIME    FROM  PJ_O_SENDFORM_ITEM         WHERE  (SENDNUM = ?) ORDER BY SEQNO ASC
[2m2025-08-06 01:41:21.843[0;39m [32mDEBUG[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, SEQNO, COIN_TYPE, COIN_YEAR AS year, FACE_VALUE, COIN_VERSION AS version, QUANTITY, GRADE_FEE, REMARK, DIY_CODE, SERIAL_NUMBER, COIN_NAME1, COIN_NAME2, COIN_NAME3, FACE_VAL, BELONG_NAME, WEIGHT_NAME, MATERIAL, BOX_TYPE, STANDARD_PRICE, INTERNATIONAL_PRICE, FEE, DISCOUNT, COIN_SIZE, COIN_WEIGHT, YEAR_INFO, REGION, TAX_TYPE, BOX_FEE, URGENT_FEE, CATALOG, RANK, BANK_NAME, SPECIAL_MARK, INTERNAL_NOTE, EXTERNAL_NOTE, SPECIAL_LABEL, AUTHENTICITY, GRADE_SCORE_VALUE, GRADE_SCORE, SCORE_REMARKS, INSPECTION_NOTE, COIN_IMAGES, CREATE_TIME FROM PJ_O_SENDFORM_ITEM WHERE (SENDNUM = ?) ORDER BY SEQNO ASC
[2m2025-08-06 01:41:21.844[0;39m [32mDEBUG[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.P.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, SEQNO, COIN_TYPE, COIN_YEAR AS year, FACE_VALUE, COIN_VERSION AS version, QUANTITY, GRADE_FEE, REMARK, DIY_CODE, SERIAL_NUMBER, COIN_NAME1, COIN_NAME2, COIN_NAME3, FACE_VAL, BELONG_NAME, WEIGHT_NAME, MATERIAL, BOX_TYPE, STANDARD_PRICE, INTERNATIONAL_PRICE, FEE, DISCOUNT, COIN_SIZE, COIN_WEIGHT, YEAR_INFO, REGION, TAX_TYPE, BOX_FEE, URGENT_FEE, CATALOG, RANK, BANK_NAME, SPECIAL_MARK, INTERNAL_NOTE, EXTERNAL_NOTE, SPECIAL_LABEL, AUTHENTICITY, GRADE_SCORE_VALUE, GRADE_SCORE, SCORE_REMARKS, INSPECTION_NOTE, COIN_IMAGES, CREATE_TIME FROM PJ_O_SENDFORM_ITEM WHERE (SENDNUM = ?) ORDER BY SEQNO ASC
[2m2025-08-06 01:41:21.844[0;39m [32mDEBUG[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.P.selectList                  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-08-06 01:41:21.872[0;39m [32mDEBUG[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.P.selectList                  [0;39m [2m:[0;39m <==      Total: 20
[2m2025-08-06 01:49:29.094[0;39m [32mDEBUG[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,SEQNO,COIN_TYPE,COIN_YEAR AS year,FACE_VALUE,COIN_VERSION AS version,QUANTITY,GRADE_FEE,REMARK,DIY_CODE,SERIAL_NUMBER,COIN_NAME1,COIN_NAME2,COIN_NAME3,FACE_VAL,BELONG_NAME,WEIGHT_NAME,MATERIAL,BOX_TYPE,STANDARD_PRICE,INTERNATIONAL_PRICE,FEE,DISCOUNT,COIN_SIZE,COIN_WEIGHT,YEAR_INFO,REGION,TAX_TYPE,BOX_FEE,URGENT_FEE,CATALOG,RANK,BANK_NAME,SPECIAL_MARK,INTERNAL_NOTE,EXTERNAL_NOTE,SPECIAL_LABEL,AUTHENTICITY,GRADE_SCORE_VALUE,GRADE_SCORE,SCORE_REMARKS,INSPECTION_NOTE,COIN_IMAGES,CREATE_TIME    FROM  PJ_O_SENDFORM_ITEM         WHERE  (DIY_CODE = ?)
[2m2025-08-06 01:49:29.112[0;39m [32mDEBUG[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,SEQNO,COIN_TYPE,COIN_YEAR AS year,FACE_VALUE,COIN_VERSION AS version,QUANTITY,GRADE_FEE,REMARK,DIY_CODE,SERIAL_NUMBER,COIN_NAME1,COIN_NAME2,COIN_NAME3,FACE_VAL,BELONG_NAME,WEIGHT_NAME,MATERIAL,BOX_TYPE,STANDARD_PRICE,INTERNATIONAL_PRICE,FEE,DISCOUNT,COIN_SIZE,COIN_WEIGHT,YEAR_INFO,REGION,TAX_TYPE,BOX_FEE,URGENT_FEE,CATALOG,RANK,BANK_NAME,SPECIAL_MARK,INTERNAL_NOTE,EXTERNAL_NOTE,SPECIAL_LABEL,AUTHENTICITY,GRADE_SCORE_VALUE,GRADE_SCORE,SCORE_REMARKS,INSPECTION_NOTE,COIN_IMAGES,CREATE_TIME    FROM  PJ_O_SENDFORM_ITEM         WHERE  (DIY_CODE = ?)
[2m2025-08-06 01:49:29.115[0;39m [32mDEBUG[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, SEQNO, COIN_TYPE, COIN_YEAR AS year, FACE_VALUE, COIN_VERSION AS version, QUANTITY, GRADE_FEE, REMARK, DIY_CODE, SERIAL_NUMBER, COIN_NAME1, COIN_NAME2, COIN_NAME3, FACE_VAL, BELONG_NAME, WEIGHT_NAME, MATERIAL, BOX_TYPE, STANDARD_PRICE, INTERNATIONAL_PRICE, FEE, DISCOUNT, COIN_SIZE, COIN_WEIGHT, YEAR_INFO, REGION, TAX_TYPE, BOX_FEE, URGENT_FEE, CATALOG, RANK, BANK_NAME, SPECIAL_MARK, INTERNAL_NOTE, EXTERNAL_NOTE, SPECIAL_LABEL, AUTHENTICITY, GRADE_SCORE_VALUE, GRADE_SCORE, SCORE_REMARKS, INSPECTION_NOTE, COIN_IMAGES, CREATE_TIME FROM PJ_O_SENDFORM_ITEM WHERE (DIY_CODE = ?)
[2m2025-08-06 01:49:29.123[0;39m [32mDEBUG[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.P.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, SEQNO, COIN_TYPE, COIN_YEAR AS year, FACE_VALUE, COIN_VERSION AS version, QUANTITY, GRADE_FEE, REMARK, DIY_CODE, SERIAL_NUMBER, COIN_NAME1, COIN_NAME2, COIN_NAME3, FACE_VAL, BELONG_NAME, WEIGHT_NAME, MATERIAL, BOX_TYPE, STANDARD_PRICE, INTERNATIONAL_PRICE, FEE, DISCOUNT, COIN_SIZE, COIN_WEIGHT, YEAR_INFO, REGION, TAX_TYPE, BOX_FEE, URGENT_FEE, CATALOG, RANK, BANK_NAME, SPECIAL_MARK, INTERNAL_NOTE, EXTERNAL_NOTE, SPECIAL_LABEL, AUTHENTICITY, GRADE_SCORE_VALUE, GRADE_SCORE, SCORE_REMARKS, INSPECTION_NOTE, COIN_IMAGES, CREATE_TIME FROM PJ_O_SENDFORM_ITEM WHERE (DIY_CODE = ?)
[2m2025-08-06 01:49:29.128[0;39m [32mDEBUG[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.P.selectList                  [0;39m [2m:[0;39m ==> Parameters: ZK25080001(String)
[2m2025-08-06 01:49:29.136[0;39m [32mDEBUG[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.P.selectList                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-06 01:49:29.142[0;39m [32mDEBUG[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-08-06 01:49:29.155[0;39m [32mDEBUG[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-08-06 01:49:29.157[0;39m [32mDEBUG[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-08-06 01:49:29.158[0;39m [32mDEBUG[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-08-06 01:49:29.159[0;39m [32mDEBUG[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-08-06 01:49:29.171[0;39m [32mDEBUG[0;39m [35m62220[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-06 01:50:30.751[0;39m [32m INFO[0;39m [35m62220[0;39m [2m---[0;39m [2m[ionShutdownHook][0;39m [36mo.s.b.w.e.tomcat.GracefulShutdown       [0;39m [2m:[0;39m Commencing graceful shutdown. Waiting for active requests to complete
[2m2025-08-06 01:50:30.766[0;39m [32m INFO[0;39m [35m62220[0;39m [2m---[0;39m [2m[tomcat-shutdown][0;39m [36mo.s.b.w.e.tomcat.GracefulShutdown       [0;39m [2m:[0;39m Graceful shutdown complete
[2m2025-08-06 01:50:30.841[0;39m [32m INFO[0;39m [35m62220[0;39m [2m---[0;39m [2m[ionShutdownHook][0;39m [36mj.LocalContainerEntityManagerFactoryBean[0;39m [2m:[0;39m Closing JPA EntityManagerFactory for persistence unit 'default'
[2m2025-08-06 01:50:30.886[0;39m [32m INFO[0;39m [35m62220[0;39m [2m---[0;39m [2m[ionShutdownHook][0;39m [36mcom.alibaba.druid.pool.DruidDataSource  [0;39m [2m:[0;39m {dataSource-1} closing ...
[2m2025-08-06 01:50:30.950[0;39m [32m INFO[0;39m [35m62220[0;39m [2m---[0;39m [2m[ionShutdownHook][0;39m [36mcom.alibaba.druid.pool.DruidDataSource  [0;39m [2m:[0;39m {dataSource-1} closed
[2m2025-08-06 20:38:07.205[0;39m [32m INFO[0;39m [35m12430[0;39m [2m---[0;39m [2m[kground-preinit][0;39m [36mo.h.validator.internal.util.Version     [0;39m [2m:[0;39m HV000001: Hibernate Validator 8.0.2.Final
[2m2025-08-06 20:38:07.494[0;39m [32m INFO[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.payne.App                           [0;39m [2m:[0;39m Starting App using Java 17.0.15 with PID 12430 (/Users/<USER>/project/ele-admin/dev-platform/payne-server/target/classes started by paynexc in /Users/<USER>/project/ele-admin)
[2m2025-08-06 20:38:07.494[0;39m [32mDEBUG[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.payne.App                           [0;39m [2m:[0;39m Running with Spring Boot v3.5.3, Spring v6.2.8
[2m2025-08-06 20:38:07.495[0;39m [32m INFO[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.payne.App                           [0;39m [2m:[0;39m The following 1 profile is active: "dev"
[2m2025-08-06 20:38:07.575[0;39m [32m INFO[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.devtools.restart.ChangeableUrls   [0;39m [2m:[0;39m The Class-Path manifest attribute in /Users/<USER>/.m2/repository/com/oracle/ojdbc8/********.0/ojdbc8-********.0.jar referenced one or more files that do not exist: file:/Users/<USER>/.m2/repository/com/oracle/ojdbc8/********.0/oraclepki.jar
[2m2025-08-06 20:38:07.576[0;39m [32m INFO[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36m.e.DevToolsPropertyDefaultsPostProcessor[0;39m [2m:[0;39m Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
[2m2025-08-06 20:38:07.576[0;39m [32m INFO[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36m.e.DevToolsPropertyDefaultsPostProcessor[0;39m [2m:[0;39m For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
[2m2025-08-06 20:38:09.322[0;39m [32m INFO[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-08-06 20:38:09.326[0;39m [32m INFO[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data JPA repositories in DEFAULT mode.
[2m2025-08-06 20:38:09.420[0;39m [32m INFO[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 73 ms. Found 0 JPA repository interfaces.
[2m2025-08-06 20:38:09.430[0;39m [32m INFO[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-08-06 20:38:09.430[0;39m [32m INFO[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
[2m2025-08-06 20:38:09.456[0;39m [32m INFO[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 24 ms. Found 0 MongoDB repository interfaces.
[2m2025-08-06 20:38:09.468[0;39m [32m INFO[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-08-06 20:38:09.470[0;39m [32m INFO[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2m2025-08-06 20:38:09.497[0;39m [32m INFO[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 18 ms. Found 0 Redis repository interfaces.
[2m2025-08-06 20:38:11.660[0;39m [32m INFO[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat initialized with port 7070 (http)
[2m2025-08-06 20:38:11.689[0;39m [32m INFO[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.coyote.http11.Http11NioProtocol     [0;39m [2m:[0;39m Initializing ProtocolHandler ["http-nio-7070"]
[2m2025-08-06 20:38:11.691[0;39m [32m INFO[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.apache.catalina.core.StandardService  [0;39m [2m:[0;39m Starting service [Tomcat]
[2m2025-08-06 20:38:11.692[0;39m [32m INFO[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.apache.catalina.core.StandardEngine   [0;39m [2m:[0;39m Starting Servlet engine: [Apache Tomcat/10.1.42]
[2m2025-08-06 20:38:11.750[0;39m [32m INFO[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m Initializing Spring embedded WebApplicationContext
[2m2025-08-06 20:38:11.751[0;39m [32m INFO[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36mw.s.c.ServletWebServerApplicationContext[0;39m [2m:[0;39m Root WebApplicationContext: initialization completed in 4175 ms
[2m2025-08-06 20:38:12.257[0;39m [32m INFO[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.mongodb.driver.client               [0;39m [2m:[0;39m MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync|spring-boot", "version": "5.5.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "15.5"}, "platform": "Java/Azul Systems, Inc./17.0.15+6-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=MongoCredential{mechanism=null, userName='mongo_txRpa7', source='admin', password=<hidden>, mechanismProperties=<hidden>}, transportSettings=null, commandListeners=[io.micrometer.core.instrument.binder.mongodb.MongoMetricsCommandListener@cc65ea1], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@47955046, com.mongodb.Jep395RecordCodecProvider@5bcc2416, com.mongodb.KotlinCodecProvider@61b2ca5a]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[paynexc.home:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[io.micrometer.core.instrument.binder.mongodb.MongoMetricsConnectionPoolListener@37a53e65], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
[2m2025-08-06 20:38:12.377[0;39m [32m INFO[0;39m [35m12430[0;39m [2m---[0;39m [2m[nexc.home:27017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Monitor thread successfully connected to server with description ServerDescription{address=paynexc.home:27017, type=STANDALONE, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=21, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=23110833, minRoundTripTimeNanos=0}
[2m2025-08-06 20:38:12.616[0;39m [32m INFO[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m [2m:[0;39m mybatis plus join properties config complete
[2m2025-08-06 20:38:12.843[0;39m [32m INFO[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m [2m:[0;39m mybatis plus join SqlInjector init
[2m2025-08-06 20:38:12.847[0;39m [32mDEBUG[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.payne.core.handler.DecryptInterceptor@4cad5173'
[2m2025-08-06 20:38:12.847[0;39m [32mDEBUG[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@6dafdb99'
[2m2025-08-06 20:38:12.847[0;39m [32mDEBUG[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.github.yulichang.interceptor.MPJInterceptor@337d97e3'
[2m2025-08-06 20:38:13.150[0;39m [32mDEBUG[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-server/target/classes/com/payne/server/banknote/mapper/xml/PjOSendformItemMapper.xml]'
[2m2025-08-06 20:38:13.229[0;39m [32mDEBUG[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-server/target/classes/com/payne/server/banknote/mapper/xml/PjOSendformMapper.xml]'
[2m2025-08-06 20:38:13.276[0;39m [32mDEBUG[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/code/mapper/xml/CodeCommonMapper.xml]'
[2m2025-08-06 20:38:13.311[0;39m [32mDEBUG[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/code/mapper/xml/CodeTypeMapper.xml]'
[2m2025-08-06 20:38:13.345[0;39m [32mDEBUG[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/LoginRecordMapper.xml]'
[2m2025-08-06 20:38:13.379[0;39m [32mDEBUG[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/RoleMenuMapper.xml]'
[2m2025-08-06 20:38:13.411[0;39m [32mDEBUG[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/SysAccountMapper.xml]'
[2m2025-08-06 20:38:13.436[0;39m [32mDEBUG[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/SysAccountRoleMapper.xml]'
[2m2025-08-06 20:38:13.462[0;39m [32mDEBUG[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/SysMenuMapper.xml]'
[2m2025-08-06 20:38:13.485[0;39m [32mDEBUG[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/SysParamMapper.xml]'
[2m2025-08-06 20:38:13.517[0;39m [32mDEBUG[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/SysRoleMapper.xml]'
[2m2025-08-06 20:38:13.539[0;39m [32mDEBUG[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/SysRoleScopeMapper.xml]'
[2m2025-08-06 20:38:13.571[0;39m [32mDEBUG[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/UserInfoMapper.xml]'
[2m2025-08-06 20:38:13.582[0;39m [32mDEBUG[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.mybatisplus.core.toolkit.Sequence   [0;39m [2m:[0;39m Initialization Sequence datacenterId:15 workerId:12
[2m2025-08-06 20:38:13.826[0;39m [32m INFO[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.redisson.Version                    [0;39m [2m:[0;39m Redisson 3.36.0
[2m2025-08-06 20:38:13.914[0;39m [31mERROR[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36mi.n.r.d.DnsServerAddressStreamProviders [0;39m [2m:[0;39m Unable to load io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'. Use DEBUG level to see the full stack: java.lang.UnsatisfiedLinkError: failed to load the required native library
[2m2025-08-06 20:38:14.097[0;39m [32m INFO[0;39m [35m12430[0;39m [2m---[0;39m [2m[isson-netty-1-7][0;39m [36mo.redisson.connection.ConnectionsHolder [0;39m [2m:[0;39m 1 connections initialized for paynexc.home/************:6379
[2m2025-08-06 20:38:14.379[0;39m [32m INFO[0;39m [35m12430[0;39m [2m---[0;39m [2m[sson-netty-1-20][0;39m [36mo.redisson.connection.ConnectionsHolder [0;39m [2m:[0;39m 24 connections initialized for paynexc.home/************:6379
[2m2025-08-06 20:38:14.540[0;39m [32mDEBUG[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.p.a.security.JwtAuthenticationFilter  [0;39m [2m:[0;39m Filter 'jwtAuthenticationFilter' configured for use
[2m2025-08-06 20:38:14.719[0;39m [32m INFO[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.hibernate.jpa.internal.util.LogHelper [0;39m [2m:[0;39m HHH000204: Processing PersistenceUnitInfo [name: default]
[2m2025-08-06 20:38:14.783[0;39m [32m INFO[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.Version                   [0;39m [2m:[0;39m HHH000412: Hibernate ORM core version 6.6.18.Final
[2m2025-08-06 20:38:14.844[0;39m [32m INFO[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.h.c.internal.RegionFactoryInitiator   [0;39m [2m:[0;39m HHH000026: Second-level cache disabled
[2m2025-08-06 20:38:15.100[0;39m [32m INFO[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.o.j.p.SpringPersistenceUnitInfo     [0;39m [2m:[0;39m No LoadTimeWeaver setup: ignoring JPA class transformer
[2m2025-08-06 20:38:15.208[0;39m [32m INFO[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.alibaba.druid.pool.DruidDataSource  [0;39m [2m:[0;39m {dataSource-1} inited
[2m2025-08-06 20:38:15.804[0;39m [33m WARN[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.orm.deprecation           [0;39m [2m:[0;39m HHH90000025: OracleDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
[2m2025-08-06 20:38:15.844[0;39m [32m INFO[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.orm.connections.pooling   [0;39m [2m:[0;39m HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource '{
	CreateTime:"2025-08-06 20:38:12",
	ActiveCount:0,
	PoolingCount:1,
	CreateCount:1,
	DestroyCount:0,
	CloseCount:1,
	ConnectCount:1,
	Connections:[
		{ID:1819833443, ConnectTime:"2025-08-06 20:38:15", UseCount:1, LastActiveTime:"2025-08-06 20:38:15"}
	]
}']
	Database driver: undefined/unknown
	Database version: 19.3
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
[2m2025-08-06 20:38:17.184[0;39m [32m INFO[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.h.e.t.j.p.i.JtaPlatformInitiator      [0;39m [2m:[0;39m HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
[2m2025-08-06 20:38:17.203[0;39m [32m INFO[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36mj.LocalContainerEntityManagerFactoryBean[0;39m [2m:[0;39m Initialized JPA EntityManagerFactory for persistence unit 'default'
[2m2025-08-06 20:38:18.321[0;39m [33m WARN[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36mJpaBaseConfiguration$JpaWebConfiguration[0;39m [2m:[0;39m spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
[2m2025-08-06 20:38:18.759[0;39m [32m INFO[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.a.e.web.EndpointLinksResolver     [0;39m [2m:[0;39m Exposing 1 endpoint beneath base path '/actuator'
[2m2025-08-06 20:38:18.807[0;39m [33m WARN[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.s.UserDetailsServiceAutoConfiguration[0;39m [2m:[0;39m 

Using generated security password: ed2c06a1-7e83-4bcc-a8b8-35ef6c2bc712

This generated password is for development use only. Your security configuration must be updated before running your application in production.

[2m2025-08-06 20:38:18.818[0;39m [32m INFO[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36mr$InitializeUserDetailsManagerConfigurer[0;39m [2m:[0;39m Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
[2m2025-08-06 20:38:19.292[0;39m [32m INFO[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.d.a.OptionalLiveReloadServer      [0;39m [2m:[0;39m LiveReload server is running on port 35729
[2m2025-08-06 20:38:19.617[0;39m [32m INFO[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36mctiveUserDetailsServiceAutoConfiguration[0;39m [2m:[0;39m 

Using generated security password: ed2c06a1-7e83-4bcc-a8b8-35ef6c2bc712

[2m2025-08-06 20:38:19.767[0;39m [32m INFO[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.coyote.http11.Http11NioProtocol     [0;39m [2m:[0;39m Starting ProtocolHandler ["http-nio-7070"]
[2m2025-08-06 20:38:19.806[0;39m [32m INFO[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat started on port 7070 (http) with context path '/'
[2m2025-08-06 20:38:19.820[0;39m [32m INFO[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.payne.App                           [0;39m [2m:[0;39m Started App in 13.408 seconds (process running for 16.065)
[2m2025-08-06 20:38:19.830[0;39m [32mDEBUG[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.a.DdlApplicationRunner            [0;39m [2m:[0;39m   ...  DDL start create  ...  
[2m2025-08-06 20:38:19.831[0;39m [32mDEBUG[0;39m [35m12430[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.a.DdlApplicationRunner            [0;39m [2m:[0;39m   ...  DDL end create  ...  
[2m2025-08-06 20:38:20.633[0;39m [32m INFO[0;39m [35m12430[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
[2m2025-08-06 20:38:20.633[0;39m [32m INFO[0;39m [35m12430[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Initializing Servlet 'dispatcherServlet'
[2m2025-08-06 20:38:20.637[0;39m [32m INFO[0;39m [35m12430[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Completed initialization in 3 ms
[2m2025-08-06 20:38:24.508[0;39m [32mDEBUG[0;39m [35m12430[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM
[2m2025-08-06 20:38:24.572[0;39m [32mDEBUG[0;39m [35m12430[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM
[2m2025-08-06 20:38:24.576[0;39m [32mDEBUG[0;39m [35m12430[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM
[2m2025-08-06 20:38:24.606[0;39m [32mDEBUG[0;39m [35m12430[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM PJ_O_SENDFORM
[2m2025-08-06 20:38:24.646[0;39m [32mDEBUG[0;39m [35m12430[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-06 20:38:24.669[0;39m [32mDEBUG[0;39m [35m12430[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-06 20:38:24.689[0;39m [32mDEBUG[0;39m [35m12430[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM ORDER BY inupttime DESC ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-08-06 20:38:24.691[0;39m [32mDEBUG[0;39m [35m12430[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: 10(Long), 0(Long)
[2m2025-08-06 20:38:24.721[0;39m [32mDEBUG[0;39m [35m12430[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-06 20:46:16.039[0;39m [32m INFO[0;39m [35m12430[0;39m [2m---[0;39m [2m[ionShutdownHook][0;39m [36mo.s.b.w.e.tomcat.GracefulShutdown       [0;39m [2m:[0;39m Commencing graceful shutdown. Waiting for active requests to complete
[2m2025-08-06 20:46:16.053[0;39m [32m INFO[0;39m [35m12430[0;39m [2m---[0;39m [2m[tomcat-shutdown][0;39m [36mo.s.b.w.e.tomcat.GracefulShutdown       [0;39m [2m:[0;39m Graceful shutdown complete
[2m2025-08-06 20:46:16.107[0;39m [32m INFO[0;39m [35m12430[0;39m [2m---[0;39m [2m[ionShutdownHook][0;39m [36mj.LocalContainerEntityManagerFactoryBean[0;39m [2m:[0;39m Closing JPA EntityManagerFactory for persistence unit 'default'
[2m2025-08-06 20:46:16.132[0;39m [32m INFO[0;39m [35m12430[0;39m [2m---[0;39m [2m[ionShutdownHook][0;39m [36mcom.alibaba.druid.pool.DruidDataSource  [0;39m [2m:[0;39m {dataSource-1} closing ...
[2m2025-08-06 20:46:16.142[0;39m [32m INFO[0;39m [35m12430[0;39m [2m---[0;39m [2m[ionShutdownHook][0;39m [36mcom.alibaba.druid.pool.DruidDataSource  [0;39m [2m:[0;39m {dataSource-1} closed
[2m2025-08-06 21:44:49.007[0;39m [32m INFO[0;39m [35m23163[0;39m [2m---[0;39m [2m[kground-preinit][0;39m [36mo.h.validator.internal.util.Version     [0;39m [2m:[0;39m HV000001: Hibernate Validator 8.0.2.Final
[2m2025-08-06 21:44:49.276[0;39m [32m INFO[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.payne.App                           [0;39m [2m:[0;39m Starting App using Java 17.0.15 with PID 23163 (/Users/<USER>/project/ele-admin/dev-platform/payne-server/target/classes started by paynexc in /Users/<USER>/project/ele-admin)
[2m2025-08-06 21:44:49.277[0;39m [32mDEBUG[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.payne.App                           [0;39m [2m:[0;39m Running with Spring Boot v3.5.3, Spring v6.2.8
[2m2025-08-06 21:44:49.278[0;39m [32m INFO[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.payne.App                           [0;39m [2m:[0;39m The following 1 profile is active: "dev"
[2m2025-08-06 21:44:49.356[0;39m [32m INFO[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.devtools.restart.ChangeableUrls   [0;39m [2m:[0;39m The Class-Path manifest attribute in /Users/<USER>/.m2/repository/com/oracle/ojdbc8/********.0/ojdbc8-********.0.jar referenced one or more files that do not exist: file:/Users/<USER>/.m2/repository/com/oracle/ojdbc8/********.0/oraclepki.jar
[2m2025-08-06 21:44:49.357[0;39m [32m INFO[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36m.e.DevToolsPropertyDefaultsPostProcessor[0;39m [2m:[0;39m Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
[2m2025-08-06 21:44:49.357[0;39m [32m INFO[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36m.e.DevToolsPropertyDefaultsPostProcessor[0;39m [2m:[0;39m For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
[2m2025-08-06 21:44:50.864[0;39m [32m INFO[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-08-06 21:44:50.867[0;39m [32m INFO[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data JPA repositories in DEFAULT mode.
[2m2025-08-06 21:44:50.934[0;39m [32m INFO[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 52 ms. Found 0 JPA repository interfaces.
[2m2025-08-06 21:44:50.940[0;39m [32m INFO[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-08-06 21:44:50.940[0;39m [32m INFO[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
[2m2025-08-06 21:44:50.958[0;39m [32m INFO[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 16 ms. Found 0 MongoDB repository interfaces.
[2m2025-08-06 21:44:50.969[0;39m [32m INFO[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-08-06 21:44:50.970[0;39m [32m INFO[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2m2025-08-06 21:44:50.999[0;39m [32m INFO[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 22 ms. Found 0 Redis repository interfaces.
[2m2025-08-06 21:44:53.150[0;39m [32m INFO[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat initialized with port 7070 (http)
[2m2025-08-06 21:44:53.184[0;39m [32m INFO[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.coyote.http11.Http11NioProtocol     [0;39m [2m:[0;39m Initializing ProtocolHandler ["http-nio-7070"]
[2m2025-08-06 21:44:53.186[0;39m [32m INFO[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.apache.catalina.core.StandardService  [0;39m [2m:[0;39m Starting service [Tomcat]
[2m2025-08-06 21:44:53.187[0;39m [32m INFO[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.apache.catalina.core.StandardEngine   [0;39m [2m:[0;39m Starting Servlet engine: [Apache Tomcat/10.1.42]
[2m2025-08-06 21:44:53.261[0;39m [32m INFO[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m Initializing Spring embedded WebApplicationContext
[2m2025-08-06 21:44:53.261[0;39m [32m INFO[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36mw.s.c.ServletWebServerApplicationContext[0;39m [2m:[0;39m Root WebApplicationContext: initialization completed in 3904 ms
[2m2025-08-06 21:44:53.728[0;39m [32m INFO[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.mongodb.driver.client               [0;39m [2m:[0;39m MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync|spring-boot", "version": "5.5.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "15.5"}, "platform": "Java/Azul Systems, Inc./17.0.15+6-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=MongoCredential{mechanism=null, userName='mongo_txRpa7', source='admin', password=<hidden>, mechanismProperties=<hidden>}, transportSettings=null, commandListeners=[io.micrometer.core.instrument.binder.mongodb.MongoMetricsCommandListener@6c30efc4], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@73534675, com.mongodb.Jep395RecordCodecProvider@550482aa, com.mongodb.KotlinCodecProvider@2b7b6f58]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[paynexc.home:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[io.micrometer.core.instrument.binder.mongodb.MongoMetricsConnectionPoolListener@61427cd3], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
[2m2025-08-06 21:44:53.837[0;39m [32m INFO[0;39m [35m23163[0;39m [2m---[0;39m [2m[nexc.home:27017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Monitor thread successfully connected to server with description ServerDescription{address=paynexc.home:27017, type=STANDALONE, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=21, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=27279166, minRoundTripTimeNanos=0}
[2m2025-08-06 21:44:54.093[0;39m [32m INFO[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m [2m:[0;39m mybatis plus join properties config complete
[2m2025-08-06 21:44:54.279[0;39m [32m INFO[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m [2m:[0;39m mybatis plus join SqlInjector init
[2m2025-08-06 21:44:54.283[0;39m [32mDEBUG[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.payne.core.handler.DecryptInterceptor@49f8b57d'
[2m2025-08-06 21:44:54.283[0;39m [32mDEBUG[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@66121af9'
[2m2025-08-06 21:44:54.283[0;39m [32mDEBUG[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.github.yulichang.interceptor.MPJInterceptor@509cf73a'
[2m2025-08-06 21:44:54.564[0;39m [32mDEBUG[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-server/target/classes/com/payne/server/banknote/mapper/xml/PjOSendformItemMapper.xml]'
[2m2025-08-06 21:44:54.632[0;39m [32mDEBUG[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-server/target/classes/com/payne/server/banknote/mapper/xml/PjOSendformMapper.xml]'
[2m2025-08-06 21:44:54.673[0;39m [32mDEBUG[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/code/mapper/xml/CodeCommonMapper.xml]'
[2m2025-08-06 21:44:54.701[0;39m [32mDEBUG[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/code/mapper/xml/CodeTypeMapper.xml]'
[2m2025-08-06 21:44:54.740[0;39m [32mDEBUG[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/LoginRecordMapper.xml]'
[2m2025-08-06 21:44:54.767[0;39m [32mDEBUG[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/RoleMenuMapper.xml]'
[2m2025-08-06 21:44:54.799[0;39m [32mDEBUG[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/SysAccountMapper.xml]'
[2m2025-08-06 21:44:54.825[0;39m [32mDEBUG[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/SysAccountRoleMapper.xml]'
[2m2025-08-06 21:44:54.850[0;39m [32mDEBUG[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/SysMenuMapper.xml]'
[2m2025-08-06 21:44:54.874[0;39m [32mDEBUG[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/SysParamMapper.xml]'
[2m2025-08-06 21:44:54.896[0;39m [32mDEBUG[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/SysRoleMapper.xml]'
[2m2025-08-06 21:44:54.919[0;39m [32mDEBUG[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/SysRoleScopeMapper.xml]'
[2m2025-08-06 21:44:54.947[0;39m [32mDEBUG[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/UserInfoMapper.xml]'
[2m2025-08-06 21:44:54.959[0;39m [32mDEBUG[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.mybatisplus.core.toolkit.Sequence   [0;39m [2m:[0;39m Initialization Sequence datacenterId:15 workerId:9
[2m2025-08-06 21:44:55.258[0;39m [32m INFO[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.redisson.Version                    [0;39m [2m:[0;39m Redisson 3.36.0
[2m2025-08-06 21:44:55.384[0;39m [31mERROR[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36mi.n.r.d.DnsServerAddressStreamProviders [0;39m [2m:[0;39m Unable to load io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'. Use DEBUG level to see the full stack: java.lang.UnsatisfiedLinkError: failed to load the required native library
[2m2025-08-06 21:44:55.587[0;39m [32m INFO[0;39m [35m23163[0;39m [2m---[0;39m [2m[isson-netty-1-7][0;39m [36mo.redisson.connection.ConnectionsHolder [0;39m [2m:[0;39m 1 connections initialized for paynexc.home/************:6379
[2m2025-08-06 21:44:55.941[0;39m [32m INFO[0;39m [35m23163[0;39m [2m---[0;39m [2m[sson-netty-1-20][0;39m [36mo.redisson.connection.ConnectionsHolder [0;39m [2m:[0;39m 24 connections initialized for paynexc.home/************:6379
[2m2025-08-06 21:44:56.149[0;39m [32mDEBUG[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.p.a.security.JwtAuthenticationFilter  [0;39m [2m:[0;39m Filter 'jwtAuthenticationFilter' configured for use
[2m2025-08-06 21:44:56.371[0;39m [32m INFO[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.hibernate.jpa.internal.util.LogHelper [0;39m [2m:[0;39m HHH000204: Processing PersistenceUnitInfo [name: default]
[2m2025-08-06 21:44:56.468[0;39m [32m INFO[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.Version                   [0;39m [2m:[0;39m HHH000412: Hibernate ORM core version 6.6.18.Final
[2m2025-08-06 21:44:56.568[0;39m [32m INFO[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.h.c.internal.RegionFactoryInitiator   [0;39m [2m:[0;39m HHH000026: Second-level cache disabled
[2m2025-08-06 21:44:56.877[0;39m [32m INFO[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.o.j.p.SpringPersistenceUnitInfo     [0;39m [2m:[0;39m No LoadTimeWeaver setup: ignoring JPA class transformer
[2m2025-08-06 21:44:57.033[0;39m [32m INFO[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.alibaba.druid.pool.DruidDataSource  [0;39m [2m:[0;39m {dataSource-1} inited
[2m2025-08-06 21:44:57.756[0;39m [33m WARN[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.orm.deprecation           [0;39m [2m:[0;39m HHH90000025: OracleDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
[2m2025-08-06 21:44:57.797[0;39m [32m INFO[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.orm.connections.pooling   [0;39m [2m:[0;39m HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource '{
	CreateTime:"2025-08-06 21:44:54",
	ActiveCount:0,
	PoolingCount:1,
	CreateCount:1,
	DestroyCount:0,
	CloseCount:1,
	ConnectCount:1,
	Connections:[
		{ID:1192532886, ConnectTime:"2025-08-06 21:44:57", UseCount:1, LastActiveTime:"2025-08-06 21:44:57"}
	]
}']
	Database driver: undefined/unknown
	Database version: 19.3
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
[2m2025-08-06 21:44:59.319[0;39m [32m INFO[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.h.e.t.j.p.i.JtaPlatformInitiator      [0;39m [2m:[0;39m HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
[2m2025-08-06 21:44:59.330[0;39m [32m INFO[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36mj.LocalContainerEntityManagerFactoryBean[0;39m [2m:[0;39m Initialized JPA EntityManagerFactory for persistence unit 'default'
[2m2025-08-06 21:45:00.373[0;39m [33m WARN[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36mJpaBaseConfiguration$JpaWebConfiguration[0;39m [2m:[0;39m spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
[2m2025-08-06 21:45:00.836[0;39m [32m INFO[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.a.e.web.EndpointLinksResolver     [0;39m [2m:[0;39m Exposing 1 endpoint beneath base path '/actuator'
[2m2025-08-06 21:45:00.881[0;39m [33m WARN[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.s.UserDetailsServiceAutoConfiguration[0;39m [2m:[0;39m 

Using generated security password: e81c279c-2126-402a-b7c8-f3f34475d06e

This generated password is for development use only. Your security configuration must be updated before running your application in production.

[2m2025-08-06 21:45:00.893[0;39m [32m INFO[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36mr$InitializeUserDetailsManagerConfigurer[0;39m [2m:[0;39m Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
[2m2025-08-06 21:45:01.392[0;39m [32m INFO[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.d.a.OptionalLiveReloadServer      [0;39m [2m:[0;39m LiveReload server is running on port 35729
[2m2025-08-06 21:45:01.736[0;39m [32m INFO[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36mctiveUserDetailsServiceAutoConfiguration[0;39m [2m:[0;39m 

Using generated security password: e81c279c-2126-402a-b7c8-f3f34475d06e

[2m2025-08-06 21:45:01.873[0;39m [32m INFO[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.coyote.http11.Http11NioProtocol     [0;39m [2m:[0;39m Starting ProtocolHandler ["http-nio-7070"]
[2m2025-08-06 21:45:01.913[0;39m [32m INFO[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat started on port 7070 (http) with context path '/'
[2m2025-08-06 21:45:01.928[0;39m [32m INFO[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.payne.App                           [0;39m [2m:[0;39m Started App in 13.631 seconds (process running for 16.104)
[2m2025-08-06 21:45:01.938[0;39m [32mDEBUG[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.a.DdlApplicationRunner            [0;39m [2m:[0;39m   ...  DDL start create  ...  
[2m2025-08-06 21:45:01.938[0;39m [32mDEBUG[0;39m [35m23163[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.a.DdlApplicationRunner            [0;39m [2m:[0;39m   ...  DDL end create  ...  
[2m2025-08-06 21:48:10.952[0;39m [32m INFO[0;39m [35m23163[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
[2m2025-08-06 21:48:10.953[0;39m [32m INFO[0;39m [35m23163[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Initializing Servlet 'dispatcherServlet'
[2m2025-08-06 21:48:10.969[0;39m [32m INFO[0;39m [35m23163[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Completed initialization in 16 ms
[2m2025-08-06 21:48:15.128[0;39m [32mDEBUG[0;39m [35m23163[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM
[2m2025-08-06 21:48:15.195[0;39m [32mDEBUG[0;39m [35m23163[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM
[2m2025-08-06 21:48:15.201[0;39m [32mDEBUG[0;39m [35m23163[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM
[2m2025-08-06 21:48:15.253[0;39m [32mDEBUG[0;39m [35m23163[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM PJ_O_SENDFORM
[2m2025-08-06 21:48:15.311[0;39m [32mDEBUG[0;39m [35m23163[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-06 21:48:15.340[0;39m [32mDEBUG[0;39m [35m23163[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-06 21:48:15.600[0;39m [32mDEBUG[0;39m [35m23163[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM ORDER BY inupttime DESC ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-08-06 21:48:15.603[0;39m [32mDEBUG[0;39m [35m23163[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: 10(Long), 0(Long)
[2m2025-08-06 21:48:15.636[0;39m [32mDEBUG[0;39m [35m23163[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-06 21:58:34.416[0;39m [32m INFO[0;39m [35m23163[0;39m [2m---[0;39m [2m[ionShutdownHook][0;39m [36mo.s.b.w.e.tomcat.GracefulShutdown       [0;39m [2m:[0;39m Commencing graceful shutdown. Waiting for active requests to complete
[2m2025-08-06 21:58:34.431[0;39m [32m INFO[0;39m [35m23163[0;39m [2m---[0;39m [2m[tomcat-shutdown][0;39m [36mo.s.b.w.e.tomcat.GracefulShutdown       [0;39m [2m:[0;39m Graceful shutdown complete
[2m2025-08-06 21:58:34.523[0;39m [32m INFO[0;39m [35m23163[0;39m [2m---[0;39m [2m[ionShutdownHook][0;39m [36mj.LocalContainerEntityManagerFactoryBean[0;39m [2m:[0;39m Closing JPA EntityManagerFactory for persistence unit 'default'
[2m2025-08-06 21:58:34.578[0;39m [32m INFO[0;39m [35m23163[0;39m [2m---[0;39m [2m[ionShutdownHook][0;39m [36mcom.alibaba.druid.pool.DruidDataSource  [0;39m [2m:[0;39m {dataSource-1} closing ...
[2m2025-08-06 23:07:10.393[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[kground-preinit][0;39m [36mo.h.validator.internal.util.Version     [0;39m [2m:[0;39m HV000001: Hibernate Validator 8.0.2.Final
[2m2025-08-06 23:07:10.672[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.payne.App                           [0;39m [2m:[0;39m Starting App using Java 17.0.15 with PID 44647 (/Users/<USER>/project/ele-admin/dev-platform/payne-server/target/classes started by paynexc in /Users/<USER>/project/ele-admin)
[2m2025-08-06 23:07:10.673[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.payne.App                           [0;39m [2m:[0;39m Running with Spring Boot v3.5.3, Spring v6.2.8
[2m2025-08-06 23:07:10.674[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.payne.App                           [0;39m [2m:[0;39m The following 1 profile is active: "dev"
[2m2025-08-06 23:07:10.760[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.devtools.restart.ChangeableUrls   [0;39m [2m:[0;39m The Class-Path manifest attribute in /Users/<USER>/.m2/repository/com/oracle/ojdbc8/********.0/ojdbc8-********.0.jar referenced one or more files that do not exist: file:/Users/<USER>/.m2/repository/com/oracle/ojdbc8/********.0/oraclepki.jar
[2m2025-08-06 23:07:10.761[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36m.e.DevToolsPropertyDefaultsPostProcessor[0;39m [2m:[0;39m Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
[2m2025-08-06 23:07:10.761[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36m.e.DevToolsPropertyDefaultsPostProcessor[0;39m [2m:[0;39m For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
[2m2025-08-06 23:07:12.285[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-08-06 23:07:12.288[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data JPA repositories in DEFAULT mode.
[2m2025-08-06 23:07:12.353[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 51 ms. Found 0 JPA repository interfaces.
[2m2025-08-06 23:07:12.361[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-08-06 23:07:12.361[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
[2m2025-08-06 23:07:12.379[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 16 ms. Found 0 MongoDB repository interfaces.
[2m2025-08-06 23:07:12.389[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-08-06 23:07:12.391[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2m2025-08-06 23:07:12.414[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 15 ms. Found 0 Redis repository interfaces.
[2m2025-08-06 23:07:13.635[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat initialized with port 7070 (http)
[2m2025-08-06 23:07:13.660[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.coyote.http11.Http11NioProtocol     [0;39m [2m:[0;39m Initializing ProtocolHandler ["http-nio-7070"]
[2m2025-08-06 23:07:13.662[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.apache.catalina.core.StandardService  [0;39m [2m:[0;39m Starting service [Tomcat]
[2m2025-08-06 23:07:13.662[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.apache.catalina.core.StandardEngine   [0;39m [2m:[0;39m Starting Servlet engine: [Apache Tomcat/10.1.42]
[2m2025-08-06 23:07:13.721[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m Initializing Spring embedded WebApplicationContext
[2m2025-08-06 23:07:13.721[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36mw.s.c.ServletWebServerApplicationContext[0;39m [2m:[0;39m Root WebApplicationContext: initialization completed in 2960 ms
[2m2025-08-06 23:07:14.158[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.mongodb.driver.client               [0;39m [2m:[0;39m MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync|spring-boot", "version": "5.5.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "15.5"}, "platform": "Java/Azul Systems, Inc./17.0.15+6-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=MongoCredential{mechanism=null, userName='mongo_txRpa7', source='admin', password=<hidden>, mechanismProperties=<hidden>}, transportSettings=null, commandListeners=[io.micrometer.core.instrument.binder.mongodb.MongoMetricsCommandListener@37dcda42], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@21dfec, com.mongodb.Jep395RecordCodecProvider@37403f66, com.mongodb.KotlinCodecProvider@da51aa8]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[paynexc.home:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[io.micrometer.core.instrument.binder.mongodb.MongoMetricsConnectionPoolListener@6b56e8ee], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
[2m2025-08-06 23:07:14.267[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[nexc.home:27017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Monitor thread successfully connected to server with description ServerDescription{address=paynexc.home:27017, type=STANDALONE, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=21, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=23130291, minRoundTripTimeNanos=0}
[2m2025-08-06 23:07:14.424[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m [2m:[0;39m mybatis plus join properties config complete
[2m2025-08-06 23:07:14.618[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m [2m:[0;39m mybatis plus join SqlInjector init
[2m2025-08-06 23:07:14.621[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.payne.core.handler.DecryptInterceptor@13485e82'
[2m2025-08-06 23:07:14.621[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@3ed3f72a'
[2m2025-08-06 23:07:14.622[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.github.yulichang.interceptor.MPJInterceptor@3413effc'
[2m2025-08-06 23:07:14.880[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-server/target/classes/com/payne/server/banknote/mapper/xml/PjOSendformItemMapper.xml]'
[2m2025-08-06 23:07:14.939[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-server/target/classes/com/payne/server/banknote/mapper/xml/PjOSendformMapper.xml]'
[2m2025-08-06 23:07:14.981[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/code/mapper/xml/CodeCommonMapper.xml]'
[2m2025-08-06 23:07:15.016[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/code/mapper/xml/CodeTypeMapper.xml]'
[2m2025-08-06 23:07:15.051[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/LoginRecordMapper.xml]'
[2m2025-08-06 23:07:15.078[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/RoleMenuMapper.xml]'
[2m2025-08-06 23:07:15.108[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/SysAccountMapper.xml]'
[2m2025-08-06 23:07:15.133[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/SysAccountRoleMapper.xml]'
[2m2025-08-06 23:07:15.158[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/SysMenuMapper.xml]'
[2m2025-08-06 23:07:15.179[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/SysParamMapper.xml]'
[2m2025-08-06 23:07:15.202[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/SysRoleMapper.xml]'
[2m2025-08-06 23:07:15.230[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/SysRoleScopeMapper.xml]'
[2m2025-08-06 23:07:15.259[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/UserInfoMapper.xml]'
[2m2025-08-06 23:07:15.266[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.mybatisplus.core.toolkit.Sequence   [0;39m [2m:[0;39m Initialization Sequence datacenterId:15 workerId:21
[2m2025-08-06 23:07:15.516[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.redisson.Version                    [0;39m [2m:[0;39m Redisson 3.36.0
[2m2025-08-06 23:07:15.601[0;39m [31mERROR[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36mi.n.r.d.DnsServerAddressStreamProviders [0;39m [2m:[0;39m Unable to load io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'. Use DEBUG level to see the full stack: java.lang.UnsatisfiedLinkError: failed to load the required native library
[2m2025-08-06 23:07:15.804[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[isson-netty-1-7][0;39m [36mo.redisson.connection.ConnectionsHolder [0;39m [2m:[0;39m 1 connections initialized for paynexc.home/************:6379
[2m2025-08-06 23:07:16.141[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[sson-netty-1-20][0;39m [36mo.redisson.connection.ConnectionsHolder [0;39m [2m:[0;39m 24 connections initialized for paynexc.home/************:6379
[2m2025-08-06 23:07:16.314[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.p.a.security.JwtAuthenticationFilter  [0;39m [2m:[0;39m Filter 'jwtAuthenticationFilter' configured for use
[2m2025-08-06 23:07:16.498[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.hibernate.jpa.internal.util.LogHelper [0;39m [2m:[0;39m HHH000204: Processing PersistenceUnitInfo [name: default]
[2m2025-08-06 23:07:16.557[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.Version                   [0;39m [2m:[0;39m HHH000412: Hibernate ORM core version 6.6.18.Final
[2m2025-08-06 23:07:16.620[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.h.c.internal.RegionFactoryInitiator   [0;39m [2m:[0;39m HHH000026: Second-level cache disabled
[2m2025-08-06 23:07:16.977[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.o.j.p.SpringPersistenceUnitInfo     [0;39m [2m:[0;39m No LoadTimeWeaver setup: ignoring JPA class transformer
[2m2025-08-06 23:07:17.131[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.alibaba.druid.pool.DruidDataSource  [0;39m [2m:[0;39m {dataSource-1} inited
[2m2025-08-06 23:07:17.848[0;39m [33m WARN[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.orm.deprecation           [0;39m [2m:[0;39m HHH90000025: OracleDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
[2m2025-08-06 23:07:17.892[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.orm.connections.pooling   [0;39m [2m:[0;39m HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource '{
	CreateTime:"2025-08-06 23:07:14",
	ActiveCount:0,
	PoolingCount:1,
	CreateCount:1,
	DestroyCount:0,
	CloseCount:1,
	ConnectCount:1,
	Connections:[
		{ID:1955693171, ConnectTime:"2025-08-06 23:07:17", UseCount:1, LastActiveTime:"2025-08-06 23:07:17"}
	]
}']
	Database driver: undefined/unknown
	Database version: 19.3
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
[2m2025-08-06 23:07:19.240[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.h.e.t.j.p.i.JtaPlatformInitiator      [0;39m [2m:[0;39m HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
[2m2025-08-06 23:07:19.251[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36mj.LocalContainerEntityManagerFactoryBean[0;39m [2m:[0;39m Initialized JPA EntityManagerFactory for persistence unit 'default'
[2m2025-08-06 23:07:20.342[0;39m [33m WARN[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36mJpaBaseConfiguration$JpaWebConfiguration[0;39m [2m:[0;39m spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
[2m2025-08-06 23:07:20.828[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.a.e.web.EndpointLinksResolver     [0;39m [2m:[0;39m Exposing 1 endpoint beneath base path '/actuator'
[2m2025-08-06 23:07:20.875[0;39m [33m WARN[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.s.UserDetailsServiceAutoConfiguration[0;39m [2m:[0;39m 

Using generated security password: 4e775aa9-dcb7-4b04-8230-0d947c6bc000

This generated password is for development use only. Your security configuration must be updated before running your application in production.

[2m2025-08-06 23:07:20.888[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36mr$InitializeUserDetailsManagerConfigurer[0;39m [2m:[0;39m Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
[2m2025-08-06 23:07:21.342[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.d.a.OptionalLiveReloadServer      [0;39m [2m:[0;39m LiveReload server is running on port 35729
[2m2025-08-06 23:07:21.634[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36mctiveUserDetailsServiceAutoConfiguration[0;39m [2m:[0;39m 

Using generated security password: 4e775aa9-dcb7-4b04-8230-0d947c6bc000

[2m2025-08-06 23:07:21.779[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.coyote.http11.Http11NioProtocol     [0;39m [2m:[0;39m Starting ProtocolHandler ["http-nio-7070"]
[2m2025-08-06 23:07:21.823[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat started on port 7070 (http) with context path '/'
[2m2025-08-06 23:07:21.840[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.payne.App                           [0;39m [2m:[0;39m Started App in 12.227 seconds (process running for 14.196)
[2m2025-08-06 23:07:21.858[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.a.DdlApplicationRunner            [0;39m [2m:[0;39m   ...  DDL start create  ...  
[2m2025-08-06 23:07:21.859[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.a.DdlApplicationRunner            [0;39m [2m:[0;39m   ...  DDL end create  ...  
[2m2025-08-06 23:07:22.548[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[2)-************][0;39m [36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
[2m2025-08-06 23:07:22.548[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[2)-************][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Initializing Servlet 'dispatcherServlet'
[2m2025-08-06 23:07:22.550[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[2)-************][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Completed initialization in 2 ms
[2m2025-08-06 23:07:26.677[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM
[2m2025-08-06 23:07:26.740[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM
[2m2025-08-06 23:07:26.745[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM
[2m2025-08-06 23:07:26.777[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM PJ_O_SENDFORM
[2m2025-08-06 23:07:26.823[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-06 23:07:26.843[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-06 23:07:26.865[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM ORDER BY inupttime DESC ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-08-06 23:07:26.867[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: 10(Long), 0(Long)
[2m2025-08-06 23:07:26.893[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-06 23:07:47.341[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT *
        FROM SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-06 23:07:47.354[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT *
        FROM SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-06 23:07:47.356[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT * FROM SYS_ROLE WHERE id IN (SELECT role_id FROM SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-06 23:07:47.357[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM SYS_ROLE WHERE id IN (SELECT role_id FROM SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-06 23:07:47.359[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==> Parameters: 13FF28E5AFA4D417E0630100007FE3FC(String)
[2m2025-08-06 23:07:47.366[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-06 23:07:52.177[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: select 
        a.ID, a.ACCOUNT_NON_EXPIRED, a.ACCOUNT_NON_LOCKED, a.CREDENTIALS_NON_EXPIRED, a.ENABLED, a.PASSWORD, a.USERNAME,a.ONLINE_NAME,a.DEPT_NAME, a.ACTIVE_FLAG,
          a.EMAIL, a.PASSWORD_LAST_UPDATE_TIME, a.END_TIME, a.GENDER, a.REAL_NAME, a.START_TIME, a.TEL_MOBILE, a.ID_TYPE, a.ID_CODE
     ,LISTAGG(d.name, ',') WITHIN GROUP(ORDER BY d.name) roleName
        from sys_account a
        , (select b.account_id, b.role_id, c.name, c.role_scope
        from sys_account_role b, sys_role c
        where b.role_id = c.id) d
        where a.id = d.account_id
         
         
         
         
         
         
         
         
         
         
         
         
         
         
     
        group by
         
        a.ID, a.ACCOUNT_NON_EXPIRED, a.ACCOUNT_NON_LOCKED, a.CREDENTIALS_NON_EXPIRED, a.ENABLED, a.PASSWORD, a.USERNAME,a.ONLINE_NAME,a.DEPT_NAME, a.ACTIVE_FLAG,
          a.EMAIL, a.PASSWORD_LAST_UPDATE_TIME, a.END_TIME, a.GENDER, a.REAL_NAME, a.START_TIME, a.TEL_MOBILE, a.ID_TYPE, a.ID_CODE
[2m2025-08-06 23:07:52.203[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: select 
        a.ID, a.ACCOUNT_NON_EXPIRED, a.ACCOUNT_NON_LOCKED, a.CREDENTIALS_NON_EXPIRED, a.ENABLED, a.PASSWORD, a.USERNAME,a.ONLINE_NAME,a.DEPT_NAME, a.ACTIVE_FLAG,
          a.EMAIL, a.PASSWORD_LAST_UPDATE_TIME, a.END_TIME, a.GENDER, a.REAL_NAME, a.START_TIME, a.TEL_MOBILE, a.ID_TYPE, a.ID_CODE
     ,LISTAGG(d.name, ',') WITHIN GROUP(ORDER BY d.name) roleName
        from sys_account a
        , (select b.account_id, b.role_id, c.name, c.role_scope
        from sys_account_role b, sys_role c
        where b.role_id = c.id) d
        where a.id = d.account_id
         
         
         
         
         
         
         
         
         
         
         
         
         
         
     
        group by
         
        a.ID, a.ACCOUNT_NON_EXPIRED, a.ACCOUNT_NON_LOCKED, a.CREDENTIALS_NON_EXPIRED, a.ENABLED, a.PASSWORD, a.USERNAME,a.ONLINE_NAME,a.DEPT_NAME, a.ACTIVE_FLAG,
          a.EMAIL, a.PASSWORD_LAST_UPDATE_TIME, a.END_TIME, a.GENDER, a.REAL_NAME, a.START_TIME, a.TEL_MOBILE, a.ID_TYPE, a.ID_CODE
[2m2025-08-06 23:07:52.207[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT a.ID, a.ACCOUNT_NON_EXPIRED, a.ACCOUNT_NON_LOCKED, a.CREDENTIALS_NON_EXPIRED, a.ENABLED, a.PASSWORD, a.USERNAME, a.ONLINE_NAME, a.DEPT_NAME, a.ACTIVE_FLAG, a.EMAIL, a.PASSWORD_LAST_UPDATE_TIME, a.END_TIME, a.GENDER, a.REAL_NAME, a.START_TIME, a.TEL_MOBILE, a.ID_TYPE, a.ID_CODE, LISTAGG(d.name, ',') WITHIN GROUP (ORDER BY d.name) roleName FROM sys_account a, (SELECT b.account_id, b.role_id, c.name, c.role_scope FROM sys_account_role b, sys_role c WHERE b.role_id = c.id) d WHERE a.id = d.account_id GROUP BY a.ID, a.ACCOUNT_NON_EXPIRED, a.ACCOUNT_NON_LOCKED, a.CREDENTIALS_NON_EXPIRED, a.ENABLED, a.PASSWORD, a.USERNAME, a.ONLINE_NAME, a.DEPT_NAME, a.ACTIVE_FLAG, a.EMAIL, a.PASSWORD_LAST_UPDATE_TIME, a.END_TIME, a.GENDER, a.REAL_NAME, a.START_TIME, a.TEL_MOBILE, a.ID_TYPE, a.ID_CODE
[2m2025-08-06 23:07:52.225[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.s.m.S.selectListRel_mpCount       [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) FROM (SELECT a.ID, a.ACCOUNT_NON_EXPIRED, a.ACCOUNT_NON_LOCKED, a.CREDENTIALS_NON_EXPIRED, a.ENABLED, a.PASSWORD, a.USERNAME, a.ONLINE_NAME, a.DEPT_NAME, a.ACTIVE_FLAG, a.EMAIL, a.PASSWORD_LAST_UPDATE_TIME, a.END_TIME, a.GENDER, a.REAL_NAME, a.START_TIME, a.TEL_MOBILE, a.ID_TYPE, a.ID_CODE, LISTAGG(d.name, ',') WITHIN GROUP (ORDER BY d.name) roleName FROM sys_account a, (SELECT b.account_id, b.role_id, c.name, c.role_scope FROM sys_account_role b, sys_role c WHERE b.role_id = c.id) d WHERE a.id = d.account_id GROUP BY a.ID, a.ACCOUNT_NON_EXPIRED, a.ACCOUNT_NON_LOCKED, a.CREDENTIALS_NON_EXPIRED, a.ENABLED, a.PASSWORD, a.USERNAME, a.ONLINE_NAME, a.DEPT_NAME, a.ACTIVE_FLAG, a.EMAIL, a.PASSWORD_LAST_UPDATE_TIME, a.END_TIME, a.GENDER, a.REAL_NAME, a.START_TIME, a.TEL_MOBILE, a.ID_TYPE, a.ID_CODE) TOTAL
[2m2025-08-06 23:07:52.225[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.s.m.S.selectListRel_mpCount       [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-06 23:07:52.250[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.s.m.S.selectListRel_mpCount       [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-06 23:07:52.251[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.s.m.S.selectListRel               [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT a.ID, a.ACCOUNT_NON_EXPIRED, a.ACCOUNT_NON_LOCKED, a.CREDENTIALS_NON_EXPIRED, a.ENABLED, a.PASSWORD, a.USERNAME, a.ONLINE_NAME, a.DEPT_NAME, a.ACTIVE_FLAG, a.EMAIL, a.PASSWORD_LAST_UPDATE_TIME, a.END_TIME, a.GENDER, a.REAL_NAME, a.START_TIME, a.TEL_MOBILE, a.ID_TYPE, a.ID_CODE, LISTAGG(d.name, ',') WITHIN GROUP (ORDER BY d.name) roleName FROM sys_account a, (SELECT b.account_id, b.role_id, c.name, c.role_scope FROM sys_account_role b, sys_role c WHERE b.role_id = c.id) d WHERE a.id = d.account_id GROUP BY a.ID, a.ACCOUNT_NON_EXPIRED, a.ACCOUNT_NON_LOCKED, a.CREDENTIALS_NON_EXPIRED, a.ENABLED, a.PASSWORD, a.USERNAME, a.ONLINE_NAME, a.DEPT_NAME, a.ACTIVE_FLAG, a.EMAIL, a.PASSWORD_LAST_UPDATE_TIME, a.END_TIME, a.GENDER, a.REAL_NAME, a.START_TIME, a.TEL_MOBILE, a.ID_TYPE, a.ID_CODE ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-08-06 23:07:52.251[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.s.m.S.selectListRel               [0;39m [2m:[0;39m ==> Parameters: 10(Long), 0(Long)
[2m2025-08-06 23:07:52.269[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.s.m.S.selectListRel               [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-06 23:07:56.180[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYS_ROLE
[2m2025-08-06 23:07:56.194[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYS_ROLE
[2m2025-08-06 23:07:56.197[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYS_ROLE
[2m2025-08-06 23:07:56.198[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYS_ROLE
[2m2025-08-06 23:07:56.198[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-06 23:07:56.204[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,ACCOUNT_NON_EXPIRED,ACCOUNT_NON_LOCKED,CREDENTIALS_NON_EXPIRED,ENABLED,PASSWORD,USERNAME,REAL_NAME,ONLINE_NAME,GENDER,ACTIVE_FLAG,ID_TYPE,ID_CODE,DEPT_NAME,TEL_MOBILE,EMAIL,START_TIME,END_TIME,PASSWORD_LAST_UPDATE_TIME    FROM  SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-06 23:07:56.212[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,ACCOUNT_NON_EXPIRED,ACCOUNT_NON_LOCKED,CREDENTIALS_NON_EXPIRED,ENABLED,PASSWORD,USERNAME,REAL_NAME,ONLINE_NAME,GENDER,ACTIVE_FLAG,ID_TYPE,ID_CODE,DEPT_NAME,TEL_MOBILE,EMAIL,START_TIME,END_TIME,PASSWORD_LAST_UPDATE_TIME    FROM  SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-06 23:07:56.214[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, ACCOUNT_NON_EXPIRED, ACCOUNT_NON_LOCKED, CREDENTIALS_NON_EXPIRED, ENABLED, PASSWORD, USERNAME, REAL_NAME, ONLINE_NAME, GENDER, ACTIVE_FLAG, ID_TYPE, ID_CODE, DEPT_NAME, TEL_MOBILE, EMAIL, START_TIME, END_TIME, PASSWORD_LAST_UPDATE_TIME FROM SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-06 23:07:56.216[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,CODE,NAME,SORT,DESCRIPTION,CODE_TYPE,ATTACHMENT    FROM  CODE_COMMON         WHERE  (code_type IN (?)) ORDER BY sort ASC
[2m2025-08-06 23:07:56.216[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,CODE,NAME,SORT,DESCRIPTION,CODE_TYPE,ATTACHMENT    FROM  CODE_COMMON         WHERE  (code_type IN (?)) ORDER BY sort ASC
[2m2025-08-06 23:07:56.216[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m <==      Total: 4
[2m2025-08-06 23:07:56.218[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, ACCOUNT_NON_EXPIRED, ACCOUNT_NON_LOCKED, CREDENTIALS_NON_EXPIRED, ENABLED, PASSWORD, USERNAME, REAL_NAME, ONLINE_NAME, GENDER, ACTIVE_FLAG, ID_TYPE, ID_CODE, DEPT_NAME, TEL_MOBILE, EMAIL, START_TIME, END_TIME, PASSWORD_LAST_UPDATE_TIME FROM SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-06 23:07:56.218[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: admin(String), admin(String)
[2m2025-08-06 23:07:56.230[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,CODE,NAME,SORT,DESCRIPTION,CODE_TYPE,ATTACHMENT    FROM  CODE_COMMON         WHERE  (code_type IN (?)) ORDER BY sort ASC
[2m2025-08-06 23:07:56.233[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, CODE, NAME, SORT, DESCRIPTION, CODE_TYPE, ATTACHMENT FROM CODE_COMMON WHERE (code_type IN (?)) ORDER BY sort ASC
[2m2025-08-06 23:07:56.234[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-06 23:07:56.235[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.c.m.CodeCommonMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, CODE, NAME, SORT, DESCRIPTION, CODE_TYPE, ATTACHMENT FROM CODE_COMMON WHERE (code_type IN (?)) ORDER BY sort ASC
[2m2025-08-06 23:07:56.235[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT *
        FROM SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-06 23:07:56.235[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.c.m.CodeCommonMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: company(String)
[2m2025-08-06 23:07:56.238[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,CODE,NAME,SORT,DESCRIPTION,CODE_TYPE,ATTACHMENT    FROM  CODE_COMMON         WHERE  (code_type IN (?)) ORDER BY sort ASC
[2m2025-08-06 23:07:56.238[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, CODE, NAME, SORT, DESCRIPTION, CODE_TYPE, ATTACHMENT FROM CODE_COMMON WHERE (code_type IN (?)) ORDER BY sort ASC
[2m2025-08-06 23:07:56.241[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT *
        FROM SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-06 23:07:56.241[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT * FROM SYS_ROLE WHERE id IN (SELECT role_id FROM SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-06 23:07:56.245[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.c.m.CodeCommonMapper.selectList   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-06 23:07:56.246[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.c.m.CodeCommonMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, CODE, NAME, SORT, DESCRIPTION, CODE_TYPE, ATTACHMENT FROM CODE_COMMON WHERE (code_type IN (?)) ORDER BY sort ASC
[2m2025-08-06 23:07:56.247[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.c.m.CodeCommonMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: zjlx(String)
[2m2025-08-06 23:07:56.251[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.c.m.CodeCommonMapper.selectList   [0;39m [2m:[0;39m <==      Total: 0
[2m2025-08-06 23:07:56.251[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM SYS_ROLE WHERE id IN (SELECT role_id FROM SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-06 23:07:56.251[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==> Parameters: 13FF28E5AFA4D417E0630100007FE3FC(String)
[2m2025-08-06 23:07:56.261[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-06 23:07:56.263[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT a.*
        FROM SYS_MENU a
         WHERE  a.menu_id IN (
            SELECT menu_id FROM SYS_ROLE_MENU WHERE role_id IN (
            SELECT ta.role_id FROM SYS_ACCOUNT_ROLE ta LEFT JOIN sys_role tb ON ta.role_id = tb.id
            WHERE ta.account_id = ? and ta.role_id = ?
            )
            )
            
            AND a.deleted = 0 
        ORDER BY a.sort_number
[2m2025-08-06 23:07:56.270[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT a.*
        FROM SYS_MENU a
         WHERE  a.menu_id IN (
            SELECT menu_id FROM SYS_ROLE_MENU WHERE role_id IN (
            SELECT ta.role_id FROM SYS_ACCOUNT_ROLE ta LEFT JOIN sys_role tb ON ta.role_id = tb.id
            WHERE ta.account_id = ? and ta.role_id = ?
            )
            )
            
            AND a.deleted = 0 
        ORDER BY a.sort_number
[2m2025-08-06 23:07:56.273[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT a.* FROM SYS_MENU a WHERE a.menu_id IN (SELECT menu_id FROM SYS_ROLE_MENU WHERE role_id IN (SELECT ta.role_id FROM SYS_ACCOUNT_ROLE ta LEFT JOIN sys_role tb ON ta.role_id = tb.id WHERE ta.account_id = ? AND ta.role_id = ?)) AND a.deleted = 0 ORDER BY a.sort_number
[2m2025-08-06 23:07:56.273[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.s.m.R.listMenuByAccount           [0;39m [2m:[0;39m ==>  Preparing: SELECT a.* FROM SYS_MENU a WHERE a.menu_id IN (SELECT menu_id FROM SYS_ROLE_MENU WHERE role_id IN (SELECT ta.role_id FROM SYS_ACCOUNT_ROLE ta LEFT JOIN sys_role tb ON ta.role_id = tb.id WHERE ta.account_id = ? AND ta.role_id = ?)) AND a.deleted = 0 ORDER BY a.sort_number
[2m2025-08-06 23:07:56.273[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.s.m.R.listMenuByAccount           [0;39m [2m:[0;39m ==> Parameters: 13FF28E5AFA4D417E0630100007FE3FC(String), 36d5ca2b3906a97f4274ffb8e8a36210(String)
[2m2025-08-06 23:07:56.310[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.s.m.R.listMenuByAccount           [0;39m [2m:[0;39m <==      Total: 71
[2m2025-08-06 23:07:56.345[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYS_ROLE
[2m2025-08-06 23:07:56.349[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYS_ROLE
[2m2025-08-06 23:07:56.351[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYS_ROLE
[2m2025-08-06 23:07:56.352[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYS_ROLE
[2m2025-08-06 23:07:56.352[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-06 23:07:56.358[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m <==      Total: 4
[2m2025-08-06 23:08:08.245[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYS_ROLE
[2m2025-08-06 23:08:08.251[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYS_ROLE
[2m2025-08-06 23:08:08.253[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYS_ROLE
[2m2025-08-06 23:08:08.253[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYS_ROLE
[2m2025-08-06 23:08:08.254[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-06 23:08:08.259[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m <==      Total: 4
[2m2025-08-06 23:08:10.832[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYS_ROLE
[2m2025-08-06 23:08:10.840[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYS_ROLE
[2m2025-08-06 23:08:10.843[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYS_ROLE
[2m2025-08-06 23:08:10.844[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYS_ROLE
[2m2025-08-06 23:08:10.844[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-06 23:08:10.850[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m <==      Total: 4
[2m2025-08-06 23:08:12.343[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,ACCOUNT_NON_EXPIRED,ACCOUNT_NON_LOCKED,CREDENTIALS_NON_EXPIRED,ENABLED,PASSWORD,USERNAME,REAL_NAME,ONLINE_NAME,GENDER,ACTIVE_FLAG,ID_TYPE,ID_CODE,DEPT_NAME,TEL_MOBILE,EMAIL,START_TIME,END_TIME,PASSWORD_LAST_UPDATE_TIME    FROM  SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-06 23:08:12.350[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,ACCOUNT_NON_EXPIRED,ACCOUNT_NON_LOCKED,CREDENTIALS_NON_EXPIRED,ENABLED,PASSWORD,USERNAME,REAL_NAME,ONLINE_NAME,GENDER,ACTIVE_FLAG,ID_TYPE,ID_CODE,DEPT_NAME,TEL_MOBILE,EMAIL,START_TIME,END_TIME,PASSWORD_LAST_UPDATE_TIME    FROM  SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-06 23:08:12.352[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, ACCOUNT_NON_EXPIRED, ACCOUNT_NON_LOCKED, CREDENTIALS_NON_EXPIRED, ENABLED, PASSWORD, USERNAME, REAL_NAME, ONLINE_NAME, GENDER, ACTIVE_FLAG, ID_TYPE, ID_CODE, DEPT_NAME, TEL_MOBILE, EMAIL, START_TIME, END_TIME, PASSWORD_LAST_UPDATE_TIME FROM SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-06 23:08:12.353[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, ACCOUNT_NON_EXPIRED, ACCOUNT_NON_LOCKED, CREDENTIALS_NON_EXPIRED, ENABLED, PASSWORD, USERNAME, REAL_NAME, ONLINE_NAME, GENDER, ACTIVE_FLAG, ID_TYPE, ID_CODE, DEPT_NAME, TEL_MOBILE, EMAIL, START_TIME, END_TIME, PASSWORD_LAST_UPDATE_TIME FROM SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-06 23:08:12.353[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: admin(String), admin(String)
[2m2025-08-06 23:08:12.363[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-06 23:08:12.363[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT *
        FROM SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-06 23:08:12.370[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT *
        FROM SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-06 23:08:12.371[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT * FROM SYS_ROLE WHERE id IN (SELECT role_id FROM SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-06 23:08:12.373[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM SYS_ROLE WHERE id IN (SELECT role_id FROM SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-06 23:08:12.374[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==> Parameters: 13FF28E5AFA4D417E0630100007FE3FC(String)
[2m2025-08-06 23:08:12.381[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-06 23:08:12.382[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT a.*
        FROM SYS_MENU a
         WHERE  a.menu_id IN (
            SELECT menu_id FROM SYS_ROLE_MENU WHERE role_id IN (
            SELECT ta.role_id FROM SYS_ACCOUNT_ROLE ta LEFT JOIN sys_role tb ON ta.role_id = tb.id
            WHERE ta.account_id = ? and ta.role_id = ?
            )
            )
            
            AND a.deleted = 0 
        ORDER BY a.sort_number
[2m2025-08-06 23:08:12.388[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT a.*
        FROM SYS_MENU a
         WHERE  a.menu_id IN (
            SELECT menu_id FROM SYS_ROLE_MENU WHERE role_id IN (
            SELECT ta.role_id FROM SYS_ACCOUNT_ROLE ta LEFT JOIN sys_role tb ON ta.role_id = tb.id
            WHERE ta.account_id = ? and ta.role_id = ?
            )
            )
            
            AND a.deleted = 0 
        ORDER BY a.sort_number
[2m2025-08-06 23:08:12.389[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT a.* FROM SYS_MENU a WHERE a.menu_id IN (SELECT menu_id FROM SYS_ROLE_MENU WHERE role_id IN (SELECT ta.role_id FROM SYS_ACCOUNT_ROLE ta LEFT JOIN sys_role tb ON ta.role_id = tb.id WHERE ta.account_id = ? AND ta.role_id = ?)) AND a.deleted = 0 ORDER BY a.sort_number
[2m2025-08-06 23:08:12.390[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.s.m.R.listMenuByAccount           [0;39m [2m:[0;39m ==>  Preparing: SELECT a.* FROM SYS_MENU a WHERE a.menu_id IN (SELECT menu_id FROM SYS_ROLE_MENU WHERE role_id IN (SELECT ta.role_id FROM SYS_ACCOUNT_ROLE ta LEFT JOIN sys_role tb ON ta.role_id = tb.id WHERE ta.account_id = ? AND ta.role_id = ?)) AND a.deleted = 0 ORDER BY a.sort_number
[2m2025-08-06 23:08:12.390[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.s.m.R.listMenuByAccount           [0;39m [2m:[0;39m ==> Parameters: 13FF28E5AFA4D417E0630100007FE3FC(String), 36d5ca2b3906a97f4274ffb8e8a36210(String)
[2m2025-08-06 23:08:12.419[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.s.m.R.listMenuByAccount           [0;39m [2m:[0;39m <==      Total: 71
[2m2025-08-06 23:08:12.478[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT ID,ACCOUNT_NON_EXPIRED,ACCOUNT_NON_LOCKED,CREDENTIALS_NON_EXPIRED,ENABLED,PASSWORD,USERNAME,REAL_NAME,ONLINE_NAME,GENDER,ACTIVE_FLAG,ID_TYPE,ID_CODE,DEPT_NAME,TEL_MOBILE,EMAIL,START_TIME,END_TIME,PASSWORD_LAST_UPDATE_TIME FROM SYS_ACCOUNT WHERE ID=?
[2m2025-08-06 23:08:12.487[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT ID,ACCOUNT_NON_EXPIRED,ACCOUNT_NON_LOCKED,CREDENTIALS_NON_EXPIRED,ENABLED,PASSWORD,USERNAME,REAL_NAME,ONLINE_NAME,GENDER,ACTIVE_FLAG,ID_TYPE,ID_CODE,DEPT_NAME,TEL_MOBILE,EMAIL,START_TIME,END_TIME,PASSWORD_LAST_UPDATE_TIME FROM SYS_ACCOUNT WHERE ID=?
[2m2025-08-06 23:08:12.489[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, ACCOUNT_NON_EXPIRED, ACCOUNT_NON_LOCKED, CREDENTIALS_NON_EXPIRED, ENABLED, PASSWORD, USERNAME, REAL_NAME, ONLINE_NAME, GENDER, ACTIVE_FLAG, ID_TYPE, ID_CODE, DEPT_NAME, TEL_MOBILE, EMAIL, START_TIME, END_TIME, PASSWORD_LAST_UPDATE_TIME FROM SYS_ACCOUNT WHERE ID = ?
[2m2025-08-06 23:08:12.489[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.SysAccountMapper.selectById   [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, ACCOUNT_NON_EXPIRED, ACCOUNT_NON_LOCKED, CREDENTIALS_NON_EXPIRED, ENABLED, PASSWORD, USERNAME, REAL_NAME, ONLINE_NAME, GENDER, ACTIVE_FLAG, ID_TYPE, ID_CODE, DEPT_NAME, TEL_MOBILE, EMAIL, START_TIME, END_TIME, PASSWORD_LAST_UPDATE_TIME FROM SYS_ACCOUNT WHERE ID = ?
[2m2025-08-06 23:08:12.489[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.SysAccountMapper.selectById   [0;39m [2m:[0;39m ==> Parameters: 13FF28E5AFA4D417E0630100007FE3FC(String)
[2m2025-08-06 23:08:12.506[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.SysAccountMapper.selectById   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-06 23:08:12.508[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: DELETE FROM  SYS_ACCOUNT_ROLE         WHERE  (account_id = ?)
[2m2025-08-06 23:08:12.514[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: DELETE FROM  SYS_ACCOUNT_ROLE         WHERE  (account_id = ?)
[2m2025-08-06 23:08:12.518[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: DELETE FROM SYS_ACCOUNT_ROLE WHERE (account_id = ?)
[2m2025-08-06 23:08:12.518[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.SysAccountRoleMapper.delete   [0;39m [2m:[0;39m ==>  Preparing: DELETE FROM SYS_ACCOUNT_ROLE WHERE (account_id = ?)
[2m2025-08-06 23:08:12.518[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.SysAccountRoleMapper.delete   [0;39m [2m:[0;39m ==> Parameters: 13FF28E5AFA4D417E0630100007FE3FC(String)
[2m2025-08-06 23:08:12.536[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.SysAccountRoleMapper.delete   [0;39m [2m:[0;39m <==    Updates: 2
[2m2025-08-06 23:08:12.536[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT ID,ACCOUNT_NON_EXPIRED,ACCOUNT_NON_LOCKED,CREDENTIALS_NON_EXPIRED,ENABLED,PASSWORD,USERNAME,REAL_NAME,ONLINE_NAME,GENDER,ACTIVE_FLAG,ID_TYPE,ID_CODE,DEPT_NAME,TEL_MOBILE,EMAIL,START_TIME,END_TIME,PASSWORD_LAST_UPDATE_TIME FROM SYS_ACCOUNT WHERE ID=?
[2m2025-08-06 23:08:12.542[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT ID,ACCOUNT_NON_EXPIRED,ACCOUNT_NON_LOCKED,CREDENTIALS_NON_EXPIRED,ENABLED,PASSWORD,USERNAME,REAL_NAME,ONLINE_NAME,GENDER,ACTIVE_FLAG,ID_TYPE,ID_CODE,DEPT_NAME,TEL_MOBILE,EMAIL,START_TIME,END_TIME,PASSWORD_LAST_UPDATE_TIME FROM SYS_ACCOUNT WHERE ID=?
[2m2025-08-06 23:08:12.544[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, ACCOUNT_NON_EXPIRED, ACCOUNT_NON_LOCKED, CREDENTIALS_NON_EXPIRED, ENABLED, PASSWORD, USERNAME, REAL_NAME, ONLINE_NAME, GENDER, ACTIVE_FLAG, ID_TYPE, ID_CODE, DEPT_NAME, TEL_MOBILE, EMAIL, START_TIME, END_TIME, PASSWORD_LAST_UPDATE_TIME FROM SYS_ACCOUNT WHERE ID = ?
[2m2025-08-06 23:08:12.544[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.SysAccountMapper.selectById   [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, ACCOUNT_NON_EXPIRED, ACCOUNT_NON_LOCKED, CREDENTIALS_NON_EXPIRED, ENABLED, PASSWORD, USERNAME, REAL_NAME, ONLINE_NAME, GENDER, ACTIVE_FLAG, ID_TYPE, ID_CODE, DEPT_NAME, TEL_MOBILE, EMAIL, START_TIME, END_TIME, PASSWORD_LAST_UPDATE_TIME FROM SYS_ACCOUNT WHERE ID = ?
[2m2025-08-06 23:08:12.545[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.SysAccountMapper.selectById   [0;39m [2m:[0;39m ==> Parameters: 13FF28E5AFA4D417E0630100007FE3FC(String)
[2m2025-08-06 23:08:12.553[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.SysAccountMapper.selectById   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-06 23:08:12.557[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: UPDATE SYS_ACCOUNT  SET ACCOUNT_NON_EXPIRED=?, ACCOUNT_NON_LOCKED=?, CREDENTIALS_NON_EXPIRED=?, ENABLED=?, PASSWORD=?, USERNAME=?, REAL_NAME=?, ONLINE_NAME=?, GENDER=?,    DEPT_NAME=?,     PASSWORD_LAST_UPDATE_TIME=?  WHERE ID=?
[2m2025-08-06 23:08:12.563[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: UPDATE SYS_ACCOUNT  SET ACCOUNT_NON_EXPIRED=?, ACCOUNT_NON_LOCKED=?, CREDENTIALS_NON_EXPIRED=?, ENABLED=?, PASSWORD=?, USERNAME=?, REAL_NAME=?, ONLINE_NAME=?, GENDER=?,    DEPT_NAME=?,     PASSWORD_LAST_UPDATE_TIME=?  WHERE ID=?
[2m2025-08-06 23:08:12.565[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: UPDATE SYS_ACCOUNT SET ACCOUNT_NON_EXPIRED = ?, ACCOUNT_NON_LOCKED = ?, CREDENTIALS_NON_EXPIRED = ?, ENABLED = ?, PASSWORD = ?, USERNAME = ?, REAL_NAME = ?, ONLINE_NAME = ?, GENDER = ?, DEPT_NAME = ?, PASSWORD_LAST_UPDATE_TIME = ? WHERE ID = ?
[2m2025-08-06 23:08:12.565[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.SysAccountMapper.updateById   [0;39m [2m:[0;39m ==>  Preparing: UPDATE SYS_ACCOUNT SET ACCOUNT_NON_EXPIRED = ?, ACCOUNT_NON_LOCKED = ?, CREDENTIALS_NON_EXPIRED = ?, ENABLED = ?, PASSWORD = ?, USERNAME = ?, REAL_NAME = ?, ONLINE_NAME = ?, GENDER = ?, DEPT_NAME = ?, PASSWORD_LAST_UPDATE_TIME = ? WHERE ID = ?
[2m2025-08-06 23:08:12.571[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.SysAccountMapper.updateById   [0;39m [2m:[0;39m ==> Parameters: true(Boolean), true(Boolean), true(Boolean), true(Boolean), $2a$10$pWYWV/xa8V2zBSK0gySiWecrzWAvAZWP3iFBSrP5jqmpN/is29xSG(String), admin(String), 系统管理员(String), 钱币评级(String), 1(Integer), 钱币评级有限公司(String), 2025-07-20T10:46:06.286539(LocalDateTime), 13FF28E5AFA4D417E0630100007FE3FC(String)
[2m2025-08-06 23:08:12.590[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.SysAccountMapper.updateById   [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-08-06 23:08:12.592[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.SysAccountRoleMapper.insert   [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO SYS_ACCOUNT_ROLE ( ID, ACCOUNT_ID, ROLE_ID ) VALUES ( ?, ?, ? )
[2m2025-08-06 23:08:12.592[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.SysAccountRoleMapper.insert   [0;39m [2m:[0;39m ==> Parameters: 564a38b548ad47276c4beeea79446562(String), 13FF28E5AFA4D417E0630100007FE3FC(String), 36d5ca2b3906a97f4274ffb8e8a36210(String)
[2m2025-08-06 23:08:12.599[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.SysAccountRoleMapper.insert   [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-08-06 23:08:12.600[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.SysAccountRoleMapper.insert   [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO SYS_ACCOUNT_ROLE ( ID, ACCOUNT_ID, ROLE_ID ) VALUES ( ?, ?, ? )
[2m2025-08-06 23:08:12.600[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.SysAccountRoleMapper.insert   [0;39m [2m:[0;39m ==> Parameters: 228d60865ccca16dfdf071a5bb95b83b(String), 13FF28E5AFA4D417E0630100007FE3FC(String), ab8f2dde6fba7c507755b8cb7d96738a(String)
[2m2025-08-06 23:08:12.605[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.SysAccountRoleMapper.insert   [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-08-06 23:08:12.605[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.SysAccountRoleMapper.insert   [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO SYS_ACCOUNT_ROLE ( ID, ACCOUNT_ID, ROLE_ID ) VALUES ( ?, ?, ? )
[2m2025-08-06 23:08:12.606[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.SysAccountRoleMapper.insert   [0;39m [2m:[0;39m ==> Parameters: a2ceba78490f18c1bc10f752f17f29ca(String), 13FF28E5AFA4D417E0630100007FE3FC(String), 38bdee6be35e0ee27731d7637449c1fb(String)
[2m2025-08-06 23:08:12.610[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.SysAccountRoleMapper.insert   [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-08-06 23:08:12.611[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.SysAccountRoleMapper.insert   [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO SYS_ACCOUNT_ROLE ( ID, ACCOUNT_ID, ROLE_ID ) VALUES ( ?, ?, ? )
[2m2025-08-06 23:08:12.612[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.SysAccountRoleMapper.insert   [0;39m [2m:[0;39m ==> Parameters: ed9e6ce23f97403af7d34ab2480c6f64(String), 13FF28E5AFA4D417E0630100007FE3FC(String), 321bf4550fd82f7b36aaf478956db669(String)
[2m2025-08-06 23:08:12.617[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.SysAccountRoleMapper.insert   [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-08-06 23:08:12.683[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYS_ROLE
[2m2025-08-06 23:08:12.683[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: select 
        a.ID, a.ACCOUNT_NON_EXPIRED, a.ACCOUNT_NON_LOCKED, a.CREDENTIALS_NON_EXPIRED, a.ENABLED, a.PASSWORD, a.USERNAME,a.ONLINE_NAME,a.DEPT_NAME, a.ACTIVE_FLAG,
          a.EMAIL, a.PASSWORD_LAST_UPDATE_TIME, a.END_TIME, a.GENDER, a.REAL_NAME, a.START_TIME, a.TEL_MOBILE, a.ID_TYPE, a.ID_CODE
     ,LISTAGG(d.name, ',') WITHIN GROUP(ORDER BY d.name) roleName
        from sys_account a
        , (select b.account_id, b.role_id, c.name, c.role_scope
        from sys_account_role b, sys_role c
        where b.role_id = c.id) d
        where a.id = d.account_id
         
         
         
         
         
         
         
         
         
         
         
         
         
         
     
        group by
         
        a.ID, a.ACCOUNT_NON_EXPIRED, a.ACCOUNT_NON_LOCKED, a.CREDENTIALS_NON_EXPIRED, a.ENABLED, a.PASSWORD, a.USERNAME,a.ONLINE_NAME,a.DEPT_NAME, a.ACTIVE_FLAG,
          a.EMAIL, a.PASSWORD_LAST_UPDATE_TIME, a.END_TIME, a.GENDER, a.REAL_NAME, a.START_TIME, a.TEL_MOBILE, a.ID_TYPE, a.ID_CODE
[2m2025-08-06 23:08:12.689[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYS_ROLE
[2m2025-08-06 23:08:12.690[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYS_ROLE
[2m2025-08-06 23:08:12.690[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYS_ROLE
[2m2025-08-06 23:08:12.692[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-06 23:08:12.697[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m <==      Total: 4
[2m2025-08-06 23:08:12.701[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: select 
        a.ID, a.ACCOUNT_NON_EXPIRED, a.ACCOUNT_NON_LOCKED, a.CREDENTIALS_NON_EXPIRED, a.ENABLED, a.PASSWORD, a.USERNAME,a.ONLINE_NAME,a.DEPT_NAME, a.ACTIVE_FLAG,
          a.EMAIL, a.PASSWORD_LAST_UPDATE_TIME, a.END_TIME, a.GENDER, a.REAL_NAME, a.START_TIME, a.TEL_MOBILE, a.ID_TYPE, a.ID_CODE
     ,LISTAGG(d.name, ',') WITHIN GROUP(ORDER BY d.name) roleName
        from sys_account a
        , (select b.account_id, b.role_id, c.name, c.role_scope
        from sys_account_role b, sys_role c
        where b.role_id = c.id) d
        where a.id = d.account_id
         
         
         
         
         
         
         
         
         
         
         
         
         
         
     
        group by
         
        a.ID, a.ACCOUNT_NON_EXPIRED, a.ACCOUNT_NON_LOCKED, a.CREDENTIALS_NON_EXPIRED, a.ENABLED, a.PASSWORD, a.USERNAME,a.ONLINE_NAME,a.DEPT_NAME, a.ACTIVE_FLAG,
          a.EMAIL, a.PASSWORD_LAST_UPDATE_TIME, a.END_TIME, a.GENDER, a.REAL_NAME, a.START_TIME, a.TEL_MOBILE, a.ID_TYPE, a.ID_CODE
[2m2025-08-06 23:08:12.703[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT a.ID, a.ACCOUNT_NON_EXPIRED, a.ACCOUNT_NON_LOCKED, a.CREDENTIALS_NON_EXPIRED, a.ENABLED, a.PASSWORD, a.USERNAME, a.ONLINE_NAME, a.DEPT_NAME, a.ACTIVE_FLAG, a.EMAIL, a.PASSWORD_LAST_UPDATE_TIME, a.END_TIME, a.GENDER, a.REAL_NAME, a.START_TIME, a.TEL_MOBILE, a.ID_TYPE, a.ID_CODE, LISTAGG(d.name, ',') WITHIN GROUP (ORDER BY d.name) roleName FROM sys_account a, (SELECT b.account_id, b.role_id, c.name, c.role_scope FROM sys_account_role b, sys_role c WHERE b.role_id = c.id) d WHERE a.id = d.account_id GROUP BY a.ID, a.ACCOUNT_NON_EXPIRED, a.ACCOUNT_NON_LOCKED, a.CREDENTIALS_NON_EXPIRED, a.ENABLED, a.PASSWORD, a.USERNAME, a.ONLINE_NAME, a.DEPT_NAME, a.ACTIVE_FLAG, a.EMAIL, a.PASSWORD_LAST_UPDATE_TIME, a.END_TIME, a.GENDER, a.REAL_NAME, a.START_TIME, a.TEL_MOBILE, a.ID_TYPE, a.ID_CODE
[2m2025-08-06 23:08:12.718[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.s.m.S.selectListRel_mpCount       [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) FROM (SELECT a.ID, a.ACCOUNT_NON_EXPIRED, a.ACCOUNT_NON_LOCKED, a.CREDENTIALS_NON_EXPIRED, a.ENABLED, a.PASSWORD, a.USERNAME, a.ONLINE_NAME, a.DEPT_NAME, a.ACTIVE_FLAG, a.EMAIL, a.PASSWORD_LAST_UPDATE_TIME, a.END_TIME, a.GENDER, a.REAL_NAME, a.START_TIME, a.TEL_MOBILE, a.ID_TYPE, a.ID_CODE, LISTAGG(d.name, ',') WITHIN GROUP (ORDER BY d.name) roleName FROM sys_account a, (SELECT b.account_id, b.role_id, c.name, c.role_scope FROM sys_account_role b, sys_role c WHERE b.role_id = c.id) d WHERE a.id = d.account_id GROUP BY a.ID, a.ACCOUNT_NON_EXPIRED, a.ACCOUNT_NON_LOCKED, a.CREDENTIALS_NON_EXPIRED, a.ENABLED, a.PASSWORD, a.USERNAME, a.ONLINE_NAME, a.DEPT_NAME, a.ACTIVE_FLAG, a.EMAIL, a.PASSWORD_LAST_UPDATE_TIME, a.END_TIME, a.GENDER, a.REAL_NAME, a.START_TIME, a.TEL_MOBILE, a.ID_TYPE, a.ID_CODE) TOTAL
[2m2025-08-06 23:08:12.718[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.s.m.S.selectListRel_mpCount       [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-06 23:08:12.727[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.s.m.S.selectListRel_mpCount       [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-06 23:08:12.727[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.s.m.S.selectListRel               [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT a.ID, a.ACCOUNT_NON_EXPIRED, a.ACCOUNT_NON_LOCKED, a.CREDENTIALS_NON_EXPIRED, a.ENABLED, a.PASSWORD, a.USERNAME, a.ONLINE_NAME, a.DEPT_NAME, a.ACTIVE_FLAG, a.EMAIL, a.PASSWORD_LAST_UPDATE_TIME, a.END_TIME, a.GENDER, a.REAL_NAME, a.START_TIME, a.TEL_MOBILE, a.ID_TYPE, a.ID_CODE, LISTAGG(d.name, ',') WITHIN GROUP (ORDER BY d.name) roleName FROM sys_account a, (SELECT b.account_id, b.role_id, c.name, c.role_scope FROM sys_account_role b, sys_role c WHERE b.role_id = c.id) d WHERE a.id = d.account_id GROUP BY a.ID, a.ACCOUNT_NON_EXPIRED, a.ACCOUNT_NON_LOCKED, a.CREDENTIALS_NON_EXPIRED, a.ENABLED, a.PASSWORD, a.USERNAME, a.ONLINE_NAME, a.DEPT_NAME, a.ACTIVE_FLAG, a.EMAIL, a.PASSWORD_LAST_UPDATE_TIME, a.END_TIME, a.GENDER, a.REAL_NAME, a.START_TIME, a.TEL_MOBILE, a.ID_TYPE, a.ID_CODE ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-08-06 23:08:12.727[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.s.m.S.selectListRel               [0;39m [2m:[0;39m ==> Parameters: 10(Long), 0(Long)
[2m2025-08-06 23:08:12.736[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.s.m.S.selectListRel               [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-06 23:08:17.403[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,ACCOUNT_NON_EXPIRED,ACCOUNT_NON_LOCKED,CREDENTIALS_NON_EXPIRED,ENABLED,PASSWORD,USERNAME,REAL_NAME,ONLINE_NAME,GENDER,ACTIVE_FLAG,ID_TYPE,ID_CODE,DEPT_NAME,TEL_MOBILE,EMAIL,START_TIME,END_TIME,PASSWORD_LAST_UPDATE_TIME    FROM  SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-06 23:08:17.413[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,ACCOUNT_NON_EXPIRED,ACCOUNT_NON_LOCKED,CREDENTIALS_NON_EXPIRED,ENABLED,PASSWORD,USERNAME,REAL_NAME,ONLINE_NAME,GENDER,ACTIVE_FLAG,ID_TYPE,ID_CODE,DEPT_NAME,TEL_MOBILE,EMAIL,START_TIME,END_TIME,PASSWORD_LAST_UPDATE_TIME    FROM  SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-06 23:08:17.415[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, ACCOUNT_NON_EXPIRED, ACCOUNT_NON_LOCKED, CREDENTIALS_NON_EXPIRED, ENABLED, PASSWORD, USERNAME, REAL_NAME, ONLINE_NAME, GENDER, ACTIVE_FLAG, ID_TYPE, ID_CODE, DEPT_NAME, TEL_MOBILE, EMAIL, START_TIME, END_TIME, PASSWORD_LAST_UPDATE_TIME FROM SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-06 23:08:17.415[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, ACCOUNT_NON_EXPIRED, ACCOUNT_NON_LOCKED, CREDENTIALS_NON_EXPIRED, ENABLED, PASSWORD, USERNAME, REAL_NAME, ONLINE_NAME, GENDER, ACTIVE_FLAG, ID_TYPE, ID_CODE, DEPT_NAME, TEL_MOBILE, EMAIL, START_TIME, END_TIME, PASSWORD_LAST_UPDATE_TIME FROM SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-06 23:08:17.417[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: zhoukou01(String), zhoukou01(String)
[2m2025-08-06 23:08:17.430[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-06 23:08:17.431[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT *
        FROM SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-06 23:08:17.437[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT *
        FROM SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-06 23:08:17.439[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT * FROM SYS_ROLE WHERE id IN (SELECT role_id FROM SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-06 23:08:17.439[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM SYS_ROLE WHERE id IN (SELECT role_id FROM SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-06 23:08:17.439[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==> Parameters: 37ce24b1612d43cdb8ead60c140a7222(String)
[2m2025-08-06 23:08:17.445[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-06 23:08:17.446[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT a.*
        FROM SYS_MENU a
         WHERE  a.menu_id IN (
            SELECT menu_id FROM SYS_ROLE_MENU WHERE role_id IN (
            SELECT ta.role_id FROM SYS_ACCOUNT_ROLE ta LEFT JOIN sys_role tb ON ta.role_id = tb.id
            WHERE ta.account_id = ? and ta.role_id = ?
            )
            )
            
            AND a.deleted = 0 
        ORDER BY a.sort_number
[2m2025-08-06 23:08:17.453[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT a.*
        FROM SYS_MENU a
         WHERE  a.menu_id IN (
            SELECT menu_id FROM SYS_ROLE_MENU WHERE role_id IN (
            SELECT ta.role_id FROM SYS_ACCOUNT_ROLE ta LEFT JOIN sys_role tb ON ta.role_id = tb.id
            WHERE ta.account_id = ? and ta.role_id = ?
            )
            )
            
            AND a.deleted = 0 
        ORDER BY a.sort_number
[2m2025-08-06 23:08:17.456[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT a.* FROM SYS_MENU a WHERE a.menu_id IN (SELECT menu_id FROM SYS_ROLE_MENU WHERE role_id IN (SELECT ta.role_id FROM SYS_ACCOUNT_ROLE ta LEFT JOIN sys_role tb ON ta.role_id = tb.id WHERE ta.account_id = ? AND ta.role_id = ?)) AND a.deleted = 0 ORDER BY a.sort_number
[2m2025-08-06 23:08:17.456[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.s.m.R.listMenuByAccount           [0;39m [2m:[0;39m ==>  Preparing: SELECT a.* FROM SYS_MENU a WHERE a.menu_id IN (SELECT menu_id FROM SYS_ROLE_MENU WHERE role_id IN (SELECT ta.role_id FROM SYS_ACCOUNT_ROLE ta LEFT JOIN sys_role tb ON ta.role_id = tb.id WHERE ta.account_id = ? AND ta.role_id = ?)) AND a.deleted = 0 ORDER BY a.sort_number
[2m2025-08-06 23:08:17.456[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.s.m.R.listMenuByAccount           [0;39m [2m:[0;39m ==> Parameters: 37ce24b1612d43cdb8ead60c140a7222(String), 321bf4550fd82f7b36aaf478956db669(String)
[2m2025-08-06 23:08:17.476[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.s.m.R.listMenuByAccount           [0;39m [2m:[0;39m <==      Total: 24
[2m2025-08-06 23:08:17.506[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYS_ROLE
[2m2025-08-06 23:08:17.512[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYS_ROLE
[2m2025-08-06 23:08:17.515[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYS_ROLE
[2m2025-08-06 23:08:17.515[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYS_ROLE
[2m2025-08-06 23:08:17.515[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-06 23:08:17.520[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m <==      Total: 4
[2m2025-08-06 23:08:20.701[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,ACCOUNT_NON_EXPIRED,ACCOUNT_NON_LOCKED,CREDENTIALS_NON_EXPIRED,ENABLED,PASSWORD,USERNAME,REAL_NAME,ONLINE_NAME,GENDER,ACTIVE_FLAG,ID_TYPE,ID_CODE,DEPT_NAME,TEL_MOBILE,EMAIL,START_TIME,END_TIME,PASSWORD_LAST_UPDATE_TIME    FROM  SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-06 23:08:20.712[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,ACCOUNT_NON_EXPIRED,ACCOUNT_NON_LOCKED,CREDENTIALS_NON_EXPIRED,ENABLED,PASSWORD,USERNAME,REAL_NAME,ONLINE_NAME,GENDER,ACTIVE_FLAG,ID_TYPE,ID_CODE,DEPT_NAME,TEL_MOBILE,EMAIL,START_TIME,END_TIME,PASSWORD_LAST_UPDATE_TIME    FROM  SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-06 23:08:20.715[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, ACCOUNT_NON_EXPIRED, ACCOUNT_NON_LOCKED, CREDENTIALS_NON_EXPIRED, ENABLED, PASSWORD, USERNAME, REAL_NAME, ONLINE_NAME, GENDER, ACTIVE_FLAG, ID_TYPE, ID_CODE, DEPT_NAME, TEL_MOBILE, EMAIL, START_TIME, END_TIME, PASSWORD_LAST_UPDATE_TIME FROM SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-06 23:08:20.715[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, ACCOUNT_NON_EXPIRED, ACCOUNT_NON_LOCKED, CREDENTIALS_NON_EXPIRED, ENABLED, PASSWORD, USERNAME, REAL_NAME, ONLINE_NAME, GENDER, ACTIVE_FLAG, ID_TYPE, ID_CODE, DEPT_NAME, TEL_MOBILE, EMAIL, START_TIME, END_TIME, PASSWORD_LAST_UPDATE_TIME FROM SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-06 23:08:20.715[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: zhoukou01(String), zhoukou01(String)
[2m2025-08-06 23:08:20.724[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-06 23:08:20.725[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT *
        FROM SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-06 23:08:20.731[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT *
        FROM SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-06 23:08:20.734[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT * FROM SYS_ROLE WHERE id IN (SELECT role_id FROM SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-06 23:08:20.734[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM SYS_ROLE WHERE id IN (SELECT role_id FROM SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-06 23:08:20.734[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==> Parameters: 37ce24b1612d43cdb8ead60c140a7222(String)
[2m2025-08-06 23:08:20.740[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-06 23:08:20.741[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT a.*
        FROM SYS_MENU a
         WHERE  a.menu_id IN (
            SELECT menu_id FROM SYS_ROLE_MENU WHERE role_id IN (
            SELECT ta.role_id FROM SYS_ACCOUNT_ROLE ta LEFT JOIN sys_role tb ON ta.role_id = tb.id
            WHERE ta.account_id = ? and ta.role_id = ?
            )
            )
            
            AND a.deleted = 0 
        ORDER BY a.sort_number
[2m2025-08-06 23:08:20.748[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT a.*
        FROM SYS_MENU a
         WHERE  a.menu_id IN (
            SELECT menu_id FROM SYS_ROLE_MENU WHERE role_id IN (
            SELECT ta.role_id FROM SYS_ACCOUNT_ROLE ta LEFT JOIN sys_role tb ON ta.role_id = tb.id
            WHERE ta.account_id = ? and ta.role_id = ?
            )
            )
            
            AND a.deleted = 0 
        ORDER BY a.sort_number
[2m2025-08-06 23:08:20.750[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT a.* FROM SYS_MENU a WHERE a.menu_id IN (SELECT menu_id FROM SYS_ROLE_MENU WHERE role_id IN (SELECT ta.role_id FROM SYS_ACCOUNT_ROLE ta LEFT JOIN sys_role tb ON ta.role_id = tb.id WHERE ta.account_id = ? AND ta.role_id = ?)) AND a.deleted = 0 ORDER BY a.sort_number
[2m2025-08-06 23:08:20.750[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.s.m.R.listMenuByAccount           [0;39m [2m:[0;39m ==>  Preparing: SELECT a.* FROM SYS_MENU a WHERE a.menu_id IN (SELECT menu_id FROM SYS_ROLE_MENU WHERE role_id IN (SELECT ta.role_id FROM SYS_ACCOUNT_ROLE ta LEFT JOIN sys_role tb ON ta.role_id = tb.id WHERE ta.account_id = ? AND ta.role_id = ?)) AND a.deleted = 0 ORDER BY a.sort_number
[2m2025-08-06 23:08:20.750[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.s.m.R.listMenuByAccount           [0;39m [2m:[0;39m ==> Parameters: 37ce24b1612d43cdb8ead60c140a7222(String), 321bf4550fd82f7b36aaf478956db669(String)
[2m2025-08-06 23:08:20.772[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.s.m.R.listMenuByAccount           [0;39m [2m:[0;39m <==      Total: 24
[2m2025-08-06 23:08:20.797[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT ID,ACCOUNT_NON_EXPIRED,ACCOUNT_NON_LOCKED,CREDENTIALS_NON_EXPIRED,ENABLED,PASSWORD,USERNAME,REAL_NAME,ONLINE_NAME,GENDER,ACTIVE_FLAG,ID_TYPE,ID_CODE,DEPT_NAME,TEL_MOBILE,EMAIL,START_TIME,END_TIME,PASSWORD_LAST_UPDATE_TIME FROM SYS_ACCOUNT WHERE ID=?
[2m2025-08-06 23:08:20.804[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT ID,ACCOUNT_NON_EXPIRED,ACCOUNT_NON_LOCKED,CREDENTIALS_NON_EXPIRED,ENABLED,PASSWORD,USERNAME,REAL_NAME,ONLINE_NAME,GENDER,ACTIVE_FLAG,ID_TYPE,ID_CODE,DEPT_NAME,TEL_MOBILE,EMAIL,START_TIME,END_TIME,PASSWORD_LAST_UPDATE_TIME FROM SYS_ACCOUNT WHERE ID=?
[2m2025-08-06 23:08:20.806[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, ACCOUNT_NON_EXPIRED, ACCOUNT_NON_LOCKED, CREDENTIALS_NON_EXPIRED, ENABLED, PASSWORD, USERNAME, REAL_NAME, ONLINE_NAME, GENDER, ACTIVE_FLAG, ID_TYPE, ID_CODE, DEPT_NAME, TEL_MOBILE, EMAIL, START_TIME, END_TIME, PASSWORD_LAST_UPDATE_TIME FROM SYS_ACCOUNT WHERE ID = ?
[2m2025-08-06 23:08:20.806[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.s.m.SysAccountMapper.selectById   [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, ACCOUNT_NON_EXPIRED, ACCOUNT_NON_LOCKED, CREDENTIALS_NON_EXPIRED, ENABLED, PASSWORD, USERNAME, REAL_NAME, ONLINE_NAME, GENDER, ACTIVE_FLAG, ID_TYPE, ID_CODE, DEPT_NAME, TEL_MOBILE, EMAIL, START_TIME, END_TIME, PASSWORD_LAST_UPDATE_TIME FROM SYS_ACCOUNT WHERE ID = ?
[2m2025-08-06 23:08:20.807[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.s.m.SysAccountMapper.selectById   [0;39m [2m:[0;39m ==> Parameters: 37ce24b1612d43cdb8ead60c140a7222(String)
[2m2025-08-06 23:08:20.814[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.s.m.SysAccountMapper.selectById   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-06 23:08:20.815[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: DELETE FROM  SYS_ACCOUNT_ROLE         WHERE  (account_id = ?)
[2m2025-08-06 23:08:20.819[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: DELETE FROM  SYS_ACCOUNT_ROLE         WHERE  (account_id = ?)
[2m2025-08-06 23:08:20.821[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: DELETE FROM SYS_ACCOUNT_ROLE WHERE (account_id = ?)
[2m2025-08-06 23:08:20.821[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.s.m.SysAccountRoleMapper.delete   [0;39m [2m:[0;39m ==>  Preparing: DELETE FROM SYS_ACCOUNT_ROLE WHERE (account_id = ?)
[2m2025-08-06 23:08:20.821[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.s.m.SysAccountRoleMapper.delete   [0;39m [2m:[0;39m ==> Parameters: 37ce24b1612d43cdb8ead60c140a7222(String)
[2m2025-08-06 23:08:20.826[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.s.m.SysAccountRoleMapper.delete   [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-08-06 23:08:20.826[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT ID,ACCOUNT_NON_EXPIRED,ACCOUNT_NON_LOCKED,CREDENTIALS_NON_EXPIRED,ENABLED,PASSWORD,USERNAME,REAL_NAME,ONLINE_NAME,GENDER,ACTIVE_FLAG,ID_TYPE,ID_CODE,DEPT_NAME,TEL_MOBILE,EMAIL,START_TIME,END_TIME,PASSWORD_LAST_UPDATE_TIME FROM SYS_ACCOUNT WHERE ID=?
[2m2025-08-06 23:08:20.833[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT ID,ACCOUNT_NON_EXPIRED,ACCOUNT_NON_LOCKED,CREDENTIALS_NON_EXPIRED,ENABLED,PASSWORD,USERNAME,REAL_NAME,ONLINE_NAME,GENDER,ACTIVE_FLAG,ID_TYPE,ID_CODE,DEPT_NAME,TEL_MOBILE,EMAIL,START_TIME,END_TIME,PASSWORD_LAST_UPDATE_TIME FROM SYS_ACCOUNT WHERE ID=?
[2m2025-08-06 23:08:20.834[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, ACCOUNT_NON_EXPIRED, ACCOUNT_NON_LOCKED, CREDENTIALS_NON_EXPIRED, ENABLED, PASSWORD, USERNAME, REAL_NAME, ONLINE_NAME, GENDER, ACTIVE_FLAG, ID_TYPE, ID_CODE, DEPT_NAME, TEL_MOBILE, EMAIL, START_TIME, END_TIME, PASSWORD_LAST_UPDATE_TIME FROM SYS_ACCOUNT WHERE ID = ?
[2m2025-08-06 23:08:20.834[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.s.m.SysAccountMapper.selectById   [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, ACCOUNT_NON_EXPIRED, ACCOUNT_NON_LOCKED, CREDENTIALS_NON_EXPIRED, ENABLED, PASSWORD, USERNAME, REAL_NAME, ONLINE_NAME, GENDER, ACTIVE_FLAG, ID_TYPE, ID_CODE, DEPT_NAME, TEL_MOBILE, EMAIL, START_TIME, END_TIME, PASSWORD_LAST_UPDATE_TIME FROM SYS_ACCOUNT WHERE ID = ?
[2m2025-08-06 23:08:20.835[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.s.m.SysAccountMapper.selectById   [0;39m [2m:[0;39m ==> Parameters: 37ce24b1612d43cdb8ead60c140a7222(String)
[2m2025-08-06 23:08:20.842[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.s.m.SysAccountMapper.selectById   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-06 23:08:20.843[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: UPDATE SYS_ACCOUNT  SET ACCOUNT_NON_EXPIRED=?, ACCOUNT_NON_LOCKED=?, CREDENTIALS_NON_EXPIRED=?, ENABLED=?, PASSWORD=?, USERNAME=?, REAL_NAME=?, ONLINE_NAME=?, GENDER=?,    DEPT_NAME=?  WHERE ID=?
[2m2025-08-06 23:08:20.848[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: UPDATE SYS_ACCOUNT  SET ACCOUNT_NON_EXPIRED=?, ACCOUNT_NON_LOCKED=?, CREDENTIALS_NON_EXPIRED=?, ENABLED=?, PASSWORD=?, USERNAME=?, REAL_NAME=?, ONLINE_NAME=?, GENDER=?,    DEPT_NAME=?  WHERE ID=?
[2m2025-08-06 23:08:20.849[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: UPDATE SYS_ACCOUNT SET ACCOUNT_NON_EXPIRED = ?, ACCOUNT_NON_LOCKED = ?, CREDENTIALS_NON_EXPIRED = ?, ENABLED = ?, PASSWORD = ?, USERNAME = ?, REAL_NAME = ?, ONLINE_NAME = ?, GENDER = ?, DEPT_NAME = ? WHERE ID = ?
[2m2025-08-06 23:08:20.849[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.s.m.SysAccountMapper.updateById   [0;39m [2m:[0;39m ==>  Preparing: UPDATE SYS_ACCOUNT SET ACCOUNT_NON_EXPIRED = ?, ACCOUNT_NON_LOCKED = ?, CREDENTIALS_NON_EXPIRED = ?, ENABLED = ?, PASSWORD = ?, USERNAME = ?, REAL_NAME = ?, ONLINE_NAME = ?, GENDER = ?, DEPT_NAME = ? WHERE ID = ?
[2m2025-08-06 23:08:20.850[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.s.m.SysAccountMapper.updateById   [0;39m [2m:[0;39m ==> Parameters: true(Boolean), true(Boolean), true(Boolean), true(Boolean), $2a$10$pWYWV/xa8V2zBSK0gySiWecrzWAvAZWP3iFBSrP5jqmpN/is29xSG(String), zhoukou01(String), 周口(String), 周口纸币送评中心(String), 2(Integer), 钱币评级有限公司(String), 37ce24b1612d43cdb8ead60c140a7222(String)
[2m2025-08-06 23:08:20.864[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.s.m.SysAccountMapper.updateById   [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-08-06 23:08:20.864[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.s.m.SysAccountRoleMapper.insert   [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO SYS_ACCOUNT_ROLE ( ID, ACCOUNT_ID, ROLE_ID ) VALUES ( ?, ?, ? )
[2m2025-08-06 23:08:20.864[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.s.m.SysAccountRoleMapper.insert   [0;39m [2m:[0;39m ==> Parameters: cb89a031b3b43be541e59af0d6444849(String), 37ce24b1612d43cdb8ead60c140a7222(String), 321bf4550fd82f7b36aaf478956db669(String)
[2m2025-08-06 23:08:20.869[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.s.m.SysAccountRoleMapper.insert   [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-08-06 23:08:20.917[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: select 
        a.ID, a.ACCOUNT_NON_EXPIRED, a.ACCOUNT_NON_LOCKED, a.CREDENTIALS_NON_EXPIRED, a.ENABLED, a.PASSWORD, a.USERNAME,a.ONLINE_NAME,a.DEPT_NAME, a.ACTIVE_FLAG,
          a.EMAIL, a.PASSWORD_LAST_UPDATE_TIME, a.END_TIME, a.GENDER, a.REAL_NAME, a.START_TIME, a.TEL_MOBILE, a.ID_TYPE, a.ID_CODE
     ,LISTAGG(d.name, ',') WITHIN GROUP(ORDER BY d.name) roleName
        from sys_account a
        , (select b.account_id, b.role_id, c.name, c.role_scope
        from sys_account_role b, sys_role c
        where b.role_id = c.id) d
        where a.id = d.account_id
         
         
         
         
         
         
         
         
         
         
         
         
         
         
     
        group by
         
        a.ID, a.ACCOUNT_NON_EXPIRED, a.ACCOUNT_NON_LOCKED, a.CREDENTIALS_NON_EXPIRED, a.ENABLED, a.PASSWORD, a.USERNAME,a.ONLINE_NAME,a.DEPT_NAME, a.ACTIVE_FLAG,
          a.EMAIL, a.PASSWORD_LAST_UPDATE_TIME, a.END_TIME, a.GENDER, a.REAL_NAME, a.START_TIME, a.TEL_MOBILE, a.ID_TYPE, a.ID_CODE
[2m2025-08-06 23:08:20.922[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYS_ROLE
[2m2025-08-06 23:08:20.927[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYS_ROLE
[2m2025-08-06 23:08:20.929[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYS_ROLE
[2m2025-08-06 23:08:20.930[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYS_ROLE
[2m2025-08-06 23:08:20.930[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-06 23:08:20.934[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: select 
        a.ID, a.ACCOUNT_NON_EXPIRED, a.ACCOUNT_NON_LOCKED, a.CREDENTIALS_NON_EXPIRED, a.ENABLED, a.PASSWORD, a.USERNAME,a.ONLINE_NAME,a.DEPT_NAME, a.ACTIVE_FLAG,
          a.EMAIL, a.PASSWORD_LAST_UPDATE_TIME, a.END_TIME, a.GENDER, a.REAL_NAME, a.START_TIME, a.TEL_MOBILE, a.ID_TYPE, a.ID_CODE
     ,LISTAGG(d.name, ',') WITHIN GROUP(ORDER BY d.name) roleName
        from sys_account a
        , (select b.account_id, b.role_id, c.name, c.role_scope
        from sys_account_role b, sys_role c
        where b.role_id = c.id) d
        where a.id = d.account_id
         
         
         
         
         
         
         
         
         
         
         
         
         
         
     
        group by
         
        a.ID, a.ACCOUNT_NON_EXPIRED, a.ACCOUNT_NON_LOCKED, a.CREDENTIALS_NON_EXPIRED, a.ENABLED, a.PASSWORD, a.USERNAME,a.ONLINE_NAME,a.DEPT_NAME, a.ACTIVE_FLAG,
          a.EMAIL, a.PASSWORD_LAST_UPDATE_TIME, a.END_TIME, a.GENDER, a.REAL_NAME, a.START_TIME, a.TEL_MOBILE, a.ID_TYPE, a.ID_CODE
[2m2025-08-06 23:08:20.936[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT a.ID, a.ACCOUNT_NON_EXPIRED, a.ACCOUNT_NON_LOCKED, a.CREDENTIALS_NON_EXPIRED, a.ENABLED, a.PASSWORD, a.USERNAME, a.ONLINE_NAME, a.DEPT_NAME, a.ACTIVE_FLAG, a.EMAIL, a.PASSWORD_LAST_UPDATE_TIME, a.END_TIME, a.GENDER, a.REAL_NAME, a.START_TIME, a.TEL_MOBILE, a.ID_TYPE, a.ID_CODE, LISTAGG(d.name, ',') WITHIN GROUP (ORDER BY d.name) roleName FROM sys_account a, (SELECT b.account_id, b.role_id, c.name, c.role_scope FROM sys_account_role b, sys_role c WHERE b.role_id = c.id) d WHERE a.id = d.account_id GROUP BY a.ID, a.ACCOUNT_NON_EXPIRED, a.ACCOUNT_NON_LOCKED, a.CREDENTIALS_NON_EXPIRED, a.ENABLED, a.PASSWORD, a.USERNAME, a.ONLINE_NAME, a.DEPT_NAME, a.ACTIVE_FLAG, a.EMAIL, a.PASSWORD_LAST_UPDATE_TIME, a.END_TIME, a.GENDER, a.REAL_NAME, a.START_TIME, a.TEL_MOBILE, a.ID_TYPE, a.ID_CODE
[2m2025-08-06 23:08:20.937[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m <==      Total: 4
[2m2025-08-06 23:08:20.953[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.s.m.S.selectListRel_mpCount       [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) FROM (SELECT a.ID, a.ACCOUNT_NON_EXPIRED, a.ACCOUNT_NON_LOCKED, a.CREDENTIALS_NON_EXPIRED, a.ENABLED, a.PASSWORD, a.USERNAME, a.ONLINE_NAME, a.DEPT_NAME, a.ACTIVE_FLAG, a.EMAIL, a.PASSWORD_LAST_UPDATE_TIME, a.END_TIME, a.GENDER, a.REAL_NAME, a.START_TIME, a.TEL_MOBILE, a.ID_TYPE, a.ID_CODE, LISTAGG(d.name, ',') WITHIN GROUP (ORDER BY d.name) roleName FROM sys_account a, (SELECT b.account_id, b.role_id, c.name, c.role_scope FROM sys_account_role b, sys_role c WHERE b.role_id = c.id) d WHERE a.id = d.account_id GROUP BY a.ID, a.ACCOUNT_NON_EXPIRED, a.ACCOUNT_NON_LOCKED, a.CREDENTIALS_NON_EXPIRED, a.ENABLED, a.PASSWORD, a.USERNAME, a.ONLINE_NAME, a.DEPT_NAME, a.ACTIVE_FLAG, a.EMAIL, a.PASSWORD_LAST_UPDATE_TIME, a.END_TIME, a.GENDER, a.REAL_NAME, a.START_TIME, a.TEL_MOBILE, a.ID_TYPE, a.ID_CODE) TOTAL
[2m2025-08-06 23:08:20.953[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.s.m.S.selectListRel_mpCount       [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-06 23:08:20.961[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.s.m.S.selectListRel_mpCount       [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-06 23:08:20.962[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.s.m.S.selectListRel               [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT a.ID, a.ACCOUNT_NON_EXPIRED, a.ACCOUNT_NON_LOCKED, a.CREDENTIALS_NON_EXPIRED, a.ENABLED, a.PASSWORD, a.USERNAME, a.ONLINE_NAME, a.DEPT_NAME, a.ACTIVE_FLAG, a.EMAIL, a.PASSWORD_LAST_UPDATE_TIME, a.END_TIME, a.GENDER, a.REAL_NAME, a.START_TIME, a.TEL_MOBILE, a.ID_TYPE, a.ID_CODE, LISTAGG(d.name, ',') WITHIN GROUP (ORDER BY d.name) roleName FROM sys_account a, (SELECT b.account_id, b.role_id, c.name, c.role_scope FROM sys_account_role b, sys_role c WHERE b.role_id = c.id) d WHERE a.id = d.account_id GROUP BY a.ID, a.ACCOUNT_NON_EXPIRED, a.ACCOUNT_NON_LOCKED, a.CREDENTIALS_NON_EXPIRED, a.ENABLED, a.PASSWORD, a.USERNAME, a.ONLINE_NAME, a.DEPT_NAME, a.ACTIVE_FLAG, a.EMAIL, a.PASSWORD_LAST_UPDATE_TIME, a.END_TIME, a.GENDER, a.REAL_NAME, a.START_TIME, a.TEL_MOBILE, a.ID_TYPE, a.ID_CODE ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-08-06 23:08:20.962[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.s.m.S.selectListRel               [0;39m [2m:[0;39m ==> Parameters: 10(Long), 0(Long)
[2m2025-08-06 23:08:20.970[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.s.m.S.selectListRel               [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-06 23:08:29.172[0;39m [31mERROR[0;39m [35m44647[0;39m [2m---[0;39m [2m[        Async-1][0;39m [36m.a.i.SimpleAsyncUncaughtExceptionHandler[0;39m [2m:[0;39m Unexpected exception occurred invoking async method: public void com.payne.upms.system.service.LoginRecordService.saveAsync(java.lang.String,java.lang.Integer,java.lang.String,jakarta.servlet.http.HttpServletRequest)

java.lang.IllegalStateException: The request object has been recycled and is no longer associated with this facade
	at org.apache.catalina.connector.RequestFacade.checkFacade(RequestFacade.java:855)
	at org.apache.catalina.connector.RequestFacade.getHeader(RequestFacade.java:505)
	at jakarta.servlet.http.HttpServletRequestWrapper.getHeader(HttpServletRequestWrapper.java:82)
	at org.springframework.security.web.firewall.StrictHttpFirewall$StrictFirewalledRequest.getHeader(StrictHttpFirewall.java:724)
	at jakarta.servlet.http.HttpServletRequestWrapper.getHeader(HttpServletRequestWrapper.java:82)
	at jakarta.servlet.http.HttpServletRequestWrapper.getHeader(HttpServletRequestWrapper.java:82)
	at jakarta.servlet.http.HttpServletRequestWrapper.getHeader(HttpServletRequestWrapper.java:82)
	at cn.hutool.extra.servlet.JakartaServletUtil.getClientIPByHeader(JakartaServletUtil.java:240)
	at cn.hutool.extra.servlet.JakartaServletUtil.getClientIP(JakartaServletUtil.java:221)
	at com.payne.upms.system.service.LoginRecordService.saveAsync(LoginRecordService.java:59)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:114)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at com.payne.core.config.AsyncConfig$SecurityContextDecorator.lambda$decorate$0(AsyncConfig.java:48)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-06 23:08:35.147[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,ACCOUNT_NON_EXPIRED,ACCOUNT_NON_LOCKED,CREDENTIALS_NON_EXPIRED,ENABLED,PASSWORD,USERNAME,REAL_NAME,ONLINE_NAME,GENDER,ACTIVE_FLAG,ID_TYPE,ID_CODE,DEPT_NAME,TEL_MOBILE,EMAIL,START_TIME,END_TIME,PASSWORD_LAST_UPDATE_TIME    FROM  SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-06 23:08:35.154[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,ACCOUNT_NON_EXPIRED,ACCOUNT_NON_LOCKED,CREDENTIALS_NON_EXPIRED,ENABLED,PASSWORD,USERNAME,REAL_NAME,ONLINE_NAME,GENDER,ACTIVE_FLAG,ID_TYPE,ID_CODE,DEPT_NAME,TEL_MOBILE,EMAIL,START_TIME,END_TIME,PASSWORD_LAST_UPDATE_TIME    FROM  SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-06 23:08:35.156[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, ACCOUNT_NON_EXPIRED, ACCOUNT_NON_LOCKED, CREDENTIALS_NON_EXPIRED, ENABLED, PASSWORD, USERNAME, REAL_NAME, ONLINE_NAME, GENDER, ACTIVE_FLAG, ID_TYPE, ID_CODE, DEPT_NAME, TEL_MOBILE, EMAIL, START_TIME, END_TIME, PASSWORD_LAST_UPDATE_TIME FROM SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-06 23:08:35.156[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, ACCOUNT_NON_EXPIRED, ACCOUNT_NON_LOCKED, CREDENTIALS_NON_EXPIRED, ENABLED, PASSWORD, USERNAME, REAL_NAME, ONLINE_NAME, GENDER, ACTIVE_FLAG, ID_TYPE, ID_CODE, DEPT_NAME, TEL_MOBILE, EMAIL, START_TIME, END_TIME, PASSWORD_LAST_UPDATE_TIME FROM SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-06 23:08:35.156[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: admin(String), admin(String)
[2m2025-08-06 23:08:35.165[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-06 23:08:35.165[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT *
        FROM SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-06 23:08:35.169[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT *
        FROM SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-06 23:08:35.170[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT * FROM SYS_ROLE WHERE id IN (SELECT role_id FROM SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-06 23:08:35.171[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM SYS_ROLE WHERE id IN (SELECT role_id FROM SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-06 23:08:35.171[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==> Parameters: 13FF28E5AFA4D417E0630100007FE3FC(String)
[2m2025-08-06 23:08:35.176[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m <==      Total: 4
[2m2025-08-06 23:08:35.176[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT a.*
        FROM SYS_MENU a
         WHERE  a.menu_id IN (
            SELECT menu_id FROM SYS_ROLE_MENU WHERE role_id IN (
            SELECT ta.role_id FROM SYS_ACCOUNT_ROLE ta LEFT JOIN sys_role tb ON ta.role_id = tb.id
            WHERE ta.account_id = ? and ta.role_id = ?
            )
            )
            
            AND a.deleted = 0 
        ORDER BY a.sort_number
[2m2025-08-06 23:08:35.181[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT a.*
        FROM SYS_MENU a
         WHERE  a.menu_id IN (
            SELECT menu_id FROM SYS_ROLE_MENU WHERE role_id IN (
            SELECT ta.role_id FROM SYS_ACCOUNT_ROLE ta LEFT JOIN sys_role tb ON ta.role_id = tb.id
            WHERE ta.account_id = ? and ta.role_id = ?
            )
            )
            
            AND a.deleted = 0 
        ORDER BY a.sort_number
[2m2025-08-06 23:08:35.182[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT a.* FROM SYS_MENU a WHERE a.menu_id IN (SELECT menu_id FROM SYS_ROLE_MENU WHERE role_id IN (SELECT ta.role_id FROM SYS_ACCOUNT_ROLE ta LEFT JOIN sys_role tb ON ta.role_id = tb.id WHERE ta.account_id = ? AND ta.role_id = ?)) AND a.deleted = 0 ORDER BY a.sort_number
[2m2025-08-06 23:08:35.182[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.s.m.R.listMenuByAccount           [0;39m [2m:[0;39m ==>  Preparing: SELECT a.* FROM SYS_MENU a WHERE a.menu_id IN (SELECT menu_id FROM SYS_ROLE_MENU WHERE role_id IN (SELECT ta.role_id FROM SYS_ACCOUNT_ROLE ta LEFT JOIN sys_role tb ON ta.role_id = tb.id WHERE ta.account_id = ? AND ta.role_id = ?)) AND a.deleted = 0 ORDER BY a.sort_number
[2m2025-08-06 23:08:35.183[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.s.m.R.listMenuByAccount           [0;39m [2m:[0;39m ==> Parameters: 13FF28E5AFA4D417E0630100007FE3FC(String), 321bf4550fd82f7b36aaf478956db669(String)
[2m2025-08-06 23:08:35.203[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.s.m.R.listMenuByAccount           [0;39m [2m:[0;39m <==      Total: 24
[2m2025-08-06 23:08:38.903[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (creator LIKE ?)
[2m2025-08-06 23:08:38.918[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (creator LIKE ?)
[2m2025-08-06 23:08:38.920[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (creator LIKE ?)
[2m2025-08-06 23:08:38.935[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM PJ_O_SENDFORM WHERE (creator LIKE ?)
[2m2025-08-06 23:08:38.935[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m ==> Parameters: %admin%(String)
[2m2025-08-06 23:08:38.948[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-06 23:08:38.964[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (creator LIKE ?) ORDER BY inupttime DESC ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-08-06 23:08:38.965[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: %admin%(String), 10(Long), 0(Long)
[2m2025-08-06 23:08:38.983[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-06 23:08:41.687[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT *
        FROM SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-06 23:08:41.694[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT *
        FROM SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-06 23:08:41.696[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT * FROM SYS_ROLE WHERE id IN (SELECT role_id FROM SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-06 23:08:41.697[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM SYS_ROLE WHERE id IN (SELECT role_id FROM SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-06 23:08:41.697[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==> Parameters: 13FF28E5AFA4D417E0630100007FE3FC(String)
[2m2025-08-06 23:08:41.702[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m <==      Total: 4
[2m2025-08-06 23:08:42.854[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT *
        FROM SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )  
         AND id = ?
[2m2025-08-06 23:08:42.860[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT *
        FROM SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )  
         AND id = ?
[2m2025-08-06 23:08:42.862[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT * FROM SYS_ROLE WHERE id IN (SELECT role_id FROM SYS_ACCOUNT_ROLE WHERE account_id = ?) AND id = ?
[2m2025-08-06 23:08:42.862[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM SYS_ROLE WHERE id IN (SELECT role_id FROM SYS_ACCOUNT_ROLE WHERE account_id = ?) AND id = ?
[2m2025-08-06 23:08:42.863[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==> Parameters: 13FF28E5AFA4D417E0630100007FE3FC(String), 36d5ca2b3906a97f4274ffb8e8a36210(String)
[2m2025-08-06 23:08:42.883[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-06 23:08:42.885[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,ACCOUNT_NON_EXPIRED,ACCOUNT_NON_LOCKED,CREDENTIALS_NON_EXPIRED,ENABLED,PASSWORD,USERNAME,REAL_NAME,ONLINE_NAME,GENDER,ACTIVE_FLAG,ID_TYPE,ID_CODE,DEPT_NAME,TEL_MOBILE,EMAIL,START_TIME,END_TIME,PASSWORD_LAST_UPDATE_TIME    FROM  SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-06 23:08:42.904[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,ACCOUNT_NON_EXPIRED,ACCOUNT_NON_LOCKED,CREDENTIALS_NON_EXPIRED,ENABLED,PASSWORD,USERNAME,REAL_NAME,ONLINE_NAME,GENDER,ACTIVE_FLAG,ID_TYPE,ID_CODE,DEPT_NAME,TEL_MOBILE,EMAIL,START_TIME,END_TIME,PASSWORD_LAST_UPDATE_TIME    FROM  SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-06 23:08:42.910[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, ACCOUNT_NON_EXPIRED, ACCOUNT_NON_LOCKED, CREDENTIALS_NON_EXPIRED, ENABLED, PASSWORD, USERNAME, REAL_NAME, ONLINE_NAME, GENDER, ACTIVE_FLAG, ID_TYPE, ID_CODE, DEPT_NAME, TEL_MOBILE, EMAIL, START_TIME, END_TIME, PASSWORD_LAST_UPDATE_TIME FROM SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-06 23:08:42.910[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, ACCOUNT_NON_EXPIRED, ACCOUNT_NON_LOCKED, CREDENTIALS_NON_EXPIRED, ENABLED, PASSWORD, USERNAME, REAL_NAME, ONLINE_NAME, GENDER, ACTIVE_FLAG, ID_TYPE, ID_CODE, DEPT_NAME, TEL_MOBILE, EMAIL, START_TIME, END_TIME, PASSWORD_LAST_UPDATE_TIME FROM SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-06 23:08:42.910[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: admin(String), admin(String)
[2m2025-08-06 23:08:42.918[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-06 23:08:42.918[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT *
        FROM SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-06 23:08:42.923[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT *
        FROM SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-06 23:08:42.925[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT * FROM SYS_ROLE WHERE id IN (SELECT role_id FROM SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-06 23:08:42.925[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM SYS_ROLE WHERE id IN (SELECT role_id FROM SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-06 23:08:42.925[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==> Parameters: 13FF28E5AFA4D417E0630100007FE3FC(String)
[2m2025-08-06 23:08:42.930[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m <==      Total: 4
[2m2025-08-06 23:08:42.930[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT a.*
        FROM SYS_MENU a
         WHERE  a.menu_id IN (
            SELECT menu_id FROM SYS_ROLE_MENU WHERE role_id IN (
            SELECT ta.role_id FROM SYS_ACCOUNT_ROLE ta LEFT JOIN sys_role tb ON ta.role_id = tb.id
            WHERE ta.account_id = ? and ta.role_id = ?
            )
            )
            
            AND a.deleted = 0 
        ORDER BY a.sort_number
[2m2025-08-06 23:08:42.936[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT a.*
        FROM SYS_MENU a
         WHERE  a.menu_id IN (
            SELECT menu_id FROM SYS_ROLE_MENU WHERE role_id IN (
            SELECT ta.role_id FROM SYS_ACCOUNT_ROLE ta LEFT JOIN sys_role tb ON ta.role_id = tb.id
            WHERE ta.account_id = ? and ta.role_id = ?
            )
            )
            
            AND a.deleted = 0 
        ORDER BY a.sort_number
[2m2025-08-06 23:08:42.938[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT a.* FROM SYS_MENU a WHERE a.menu_id IN (SELECT menu_id FROM SYS_ROLE_MENU WHERE role_id IN (SELECT ta.role_id FROM SYS_ACCOUNT_ROLE ta LEFT JOIN sys_role tb ON ta.role_id = tb.id WHERE ta.account_id = ? AND ta.role_id = ?)) AND a.deleted = 0 ORDER BY a.sort_number
[2m2025-08-06 23:08:42.938[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.s.m.R.listMenuByAccount           [0;39m [2m:[0;39m ==>  Preparing: SELECT a.* FROM SYS_MENU a WHERE a.menu_id IN (SELECT menu_id FROM SYS_ROLE_MENU WHERE role_id IN (SELECT ta.role_id FROM SYS_ACCOUNT_ROLE ta LEFT JOIN sys_role tb ON ta.role_id = tb.id WHERE ta.account_id = ? AND ta.role_id = ?)) AND a.deleted = 0 ORDER BY a.sort_number
[2m2025-08-06 23:08:42.938[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.s.m.R.listMenuByAccount           [0;39m [2m:[0;39m ==> Parameters: 13FF28E5AFA4D417E0630100007FE3FC(String), 36d5ca2b3906a97f4274ffb8e8a36210(String)
[2m2025-08-06 23:08:42.973[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.s.m.R.listMenuByAccount           [0;39m [2m:[0;39m <==      Total: 71
[2m2025-08-06 23:08:43.072[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM
[2m2025-08-06 23:08:43.094[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM
[2m2025-08-06 23:08:43.094[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM
[2m2025-08-06 23:08:43.118[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM PJ_O_SENDFORM
[2m2025-08-06 23:08:43.118[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-06 23:08:43.123[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-06 23:08:43.140[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM ORDER BY inupttime DESC ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-08-06 23:08:43.141[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: 10(Long), 0(Long)
[2m2025-08-06 23:08:43.149[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-06 23:08:45.835[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT *
        FROM SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-06 23:08:45.843[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT *
        FROM SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-06 23:08:45.846[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT * FROM SYS_ROLE WHERE id IN (SELECT role_id FROM SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-06 23:08:45.847[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM SYS_ROLE WHERE id IN (SELECT role_id FROM SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-06 23:08:45.847[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==> Parameters: 13FF28E5AFA4D417E0630100007FE3FC(String)
[2m2025-08-06 23:08:45.853[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m <==      Total: 4
[2m2025-08-06 23:08:46.775[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT *
        FROM SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )  
         AND id = ?
[2m2025-08-06 23:08:46.781[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT *
        FROM SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )  
         AND id = ?
[2m2025-08-06 23:08:46.783[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT * FROM SYS_ROLE WHERE id IN (SELECT role_id FROM SYS_ACCOUNT_ROLE WHERE account_id = ?) AND id = ?
[2m2025-08-06 23:08:46.783[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM SYS_ROLE WHERE id IN (SELECT role_id FROM SYS_ACCOUNT_ROLE WHERE account_id = ?) AND id = ?
[2m2025-08-06 23:08:46.784[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==> Parameters: 13FF28E5AFA4D417E0630100007FE3FC(String), ab8f2dde6fba7c507755b8cb7d96738a(String)
[2m2025-08-06 23:08:46.788[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-06 23:08:46.789[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,ACCOUNT_NON_EXPIRED,ACCOUNT_NON_LOCKED,CREDENTIALS_NON_EXPIRED,ENABLED,PASSWORD,USERNAME,REAL_NAME,ONLINE_NAME,GENDER,ACTIVE_FLAG,ID_TYPE,ID_CODE,DEPT_NAME,TEL_MOBILE,EMAIL,START_TIME,END_TIME,PASSWORD_LAST_UPDATE_TIME    FROM  SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-06 23:08:46.796[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,ACCOUNT_NON_EXPIRED,ACCOUNT_NON_LOCKED,CREDENTIALS_NON_EXPIRED,ENABLED,PASSWORD,USERNAME,REAL_NAME,ONLINE_NAME,GENDER,ACTIVE_FLAG,ID_TYPE,ID_CODE,DEPT_NAME,TEL_MOBILE,EMAIL,START_TIME,END_TIME,PASSWORD_LAST_UPDATE_TIME    FROM  SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-06 23:08:46.801[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, ACCOUNT_NON_EXPIRED, ACCOUNT_NON_LOCKED, CREDENTIALS_NON_EXPIRED, ENABLED, PASSWORD, USERNAME, REAL_NAME, ONLINE_NAME, GENDER, ACTIVE_FLAG, ID_TYPE, ID_CODE, DEPT_NAME, TEL_MOBILE, EMAIL, START_TIME, END_TIME, PASSWORD_LAST_UPDATE_TIME FROM SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-06 23:08:46.802[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, ACCOUNT_NON_EXPIRED, ACCOUNT_NON_LOCKED, CREDENTIALS_NON_EXPIRED, ENABLED, PASSWORD, USERNAME, REAL_NAME, ONLINE_NAME, GENDER, ACTIVE_FLAG, ID_TYPE, ID_CODE, DEPT_NAME, TEL_MOBILE, EMAIL, START_TIME, END_TIME, PASSWORD_LAST_UPDATE_TIME FROM SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-06 23:08:46.806[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: admin(String), admin(String)
[2m2025-08-06 23:08:46.816[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-06 23:08:46.817[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT *
        FROM SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-06 23:08:46.827[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT *
        FROM SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-06 23:08:46.827[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT * FROM SYS_ROLE WHERE id IN (SELECT role_id FROM SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-06 23:08:46.829[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM SYS_ROLE WHERE id IN (SELECT role_id FROM SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-06 23:08:46.829[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==> Parameters: 13FF28E5AFA4D417E0630100007FE3FC(String)
[2m2025-08-06 23:08:46.834[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m <==      Total: 4
[2m2025-08-06 23:08:46.834[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT a.*
        FROM SYS_MENU a
         WHERE  a.menu_id IN (
            SELECT menu_id FROM SYS_ROLE_MENU WHERE role_id IN (
            SELECT ta.role_id FROM SYS_ACCOUNT_ROLE ta LEFT JOIN sys_role tb ON ta.role_id = tb.id
            WHERE ta.account_id = ? and ta.role_id = ?
            )
            )
            
            AND a.deleted = 0 
        ORDER BY a.sort_number
[2m2025-08-06 23:08:46.841[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT a.*
        FROM SYS_MENU a
         WHERE  a.menu_id IN (
            SELECT menu_id FROM SYS_ROLE_MENU WHERE role_id IN (
            SELECT ta.role_id FROM SYS_ACCOUNT_ROLE ta LEFT JOIN sys_role tb ON ta.role_id = tb.id
            WHERE ta.account_id = ? and ta.role_id = ?
            )
            )
            
            AND a.deleted = 0 
        ORDER BY a.sort_number
[2m2025-08-06 23:08:46.842[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT a.* FROM SYS_MENU a WHERE a.menu_id IN (SELECT menu_id FROM SYS_ROLE_MENU WHERE role_id IN (SELECT ta.role_id FROM SYS_ACCOUNT_ROLE ta LEFT JOIN sys_role tb ON ta.role_id = tb.id WHERE ta.account_id = ? AND ta.role_id = ?)) AND a.deleted = 0 ORDER BY a.sort_number
[2m2025-08-06 23:08:46.842[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.s.m.R.listMenuByAccount           [0;39m [2m:[0;39m ==>  Preparing: SELECT a.* FROM SYS_MENU a WHERE a.menu_id IN (SELECT menu_id FROM SYS_ROLE_MENU WHERE role_id IN (SELECT ta.role_id FROM SYS_ACCOUNT_ROLE ta LEFT JOIN sys_role tb ON ta.role_id = tb.id WHERE ta.account_id = ? AND ta.role_id = ?)) AND a.deleted = 0 ORDER BY a.sort_number
[2m2025-08-06 23:08:46.843[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.s.m.R.listMenuByAccount           [0;39m [2m:[0;39m ==> Parameters: 13FF28E5AFA4D417E0630100007FE3FC(String), ab8f2dde6fba7c507755b8cb7d96738a(String)
[2m2025-08-06 23:08:46.867[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.s.m.R.listMenuByAccount           [0;39m [2m:[0;39m <==      Total: 73
[2m2025-08-06 23:08:46.994[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM
[2m2025-08-06 23:08:47.007[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM
[2m2025-08-06 23:08:47.009[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM
[2m2025-08-06 23:08:47.026[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM PJ_O_SENDFORM
[2m2025-08-06 23:08:47.026[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-06 23:08:47.031[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-06 23:08:47.046[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM ORDER BY inupttime DESC ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-08-06 23:08:47.047[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: 10(Long), 0(Long)
[2m2025-08-06 23:08:47.057[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-06 23:08:49.159[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM
[2m2025-08-06 23:08:49.176[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM
[2m2025-08-06 23:08:49.178[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM
[2m2025-08-06 23:08:49.194[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM PJ_O_SENDFORM
[2m2025-08-06 23:08:49.194[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-06 23:08:49.199[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-06 23:08:49.216[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM ORDER BY inupttime DESC ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-08-06 23:08:49.217[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: 10(Long), 0(Long)
[2m2025-08-06 23:08:49.225[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-06 23:08:52.661[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT *
        FROM SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-06 23:08:52.669[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT *
        FROM SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-06 23:08:52.671[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT * FROM SYS_ROLE WHERE id IN (SELECT role_id FROM SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-06 23:08:52.672[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM SYS_ROLE WHERE id IN (SELECT role_id FROM SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-06 23:08:52.672[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==> Parameters: 13FF28E5AFA4D417E0630100007FE3FC(String)
[2m2025-08-06 23:08:52.678[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m <==      Total: 4
[2m2025-08-06 23:08:53.833[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT *
        FROM SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )  
         AND id = ?
[2m2025-08-06 23:08:53.842[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT *
        FROM SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )  
         AND id = ?
[2m2025-08-06 23:08:53.842[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT * FROM SYS_ROLE WHERE id IN (SELECT role_id FROM SYS_ACCOUNT_ROLE WHERE account_id = ?) AND id = ?
[2m2025-08-06 23:08:53.847[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM SYS_ROLE WHERE id IN (SELECT role_id FROM SYS_ACCOUNT_ROLE WHERE account_id = ?) AND id = ?
[2m2025-08-06 23:08:53.847[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==> Parameters: 13FF28E5AFA4D417E0630100007FE3FC(String), 36d5ca2b3906a97f4274ffb8e8a36210(String)
[2m2025-08-06 23:08:53.853[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-06 23:08:53.854[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,ACCOUNT_NON_EXPIRED,ACCOUNT_NON_LOCKED,CREDENTIALS_NON_EXPIRED,ENABLED,PASSWORD,USERNAME,REAL_NAME,ONLINE_NAME,GENDER,ACTIVE_FLAG,ID_TYPE,ID_CODE,DEPT_NAME,TEL_MOBILE,EMAIL,START_TIME,END_TIME,PASSWORD_LAST_UPDATE_TIME    FROM  SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-06 23:08:53.862[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,ACCOUNT_NON_EXPIRED,ACCOUNT_NON_LOCKED,CREDENTIALS_NON_EXPIRED,ENABLED,PASSWORD,USERNAME,REAL_NAME,ONLINE_NAME,GENDER,ACTIVE_FLAG,ID_TYPE,ID_CODE,DEPT_NAME,TEL_MOBILE,EMAIL,START_TIME,END_TIME,PASSWORD_LAST_UPDATE_TIME    FROM  SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-06 23:08:53.866[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, ACCOUNT_NON_EXPIRED, ACCOUNT_NON_LOCKED, CREDENTIALS_NON_EXPIRED, ENABLED, PASSWORD, USERNAME, REAL_NAME, ONLINE_NAME, GENDER, ACTIVE_FLAG, ID_TYPE, ID_CODE, DEPT_NAME, TEL_MOBILE, EMAIL, START_TIME, END_TIME, PASSWORD_LAST_UPDATE_TIME FROM SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-06 23:08:53.866[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, ACCOUNT_NON_EXPIRED, ACCOUNT_NON_LOCKED, CREDENTIALS_NON_EXPIRED, ENABLED, PASSWORD, USERNAME, REAL_NAME, ONLINE_NAME, GENDER, ACTIVE_FLAG, ID_TYPE, ID_CODE, DEPT_NAME, TEL_MOBILE, EMAIL, START_TIME, END_TIME, PASSWORD_LAST_UPDATE_TIME FROM SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-06 23:08:53.866[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: admin(String), admin(String)
[2m2025-08-06 23:08:53.875[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-06 23:08:53.876[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT *
        FROM SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-06 23:08:53.884[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT *
        FROM SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-06 23:08:53.884[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT * FROM SYS_ROLE WHERE id IN (SELECT role_id FROM SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-06 23:08:53.888[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM SYS_ROLE WHERE id IN (SELECT role_id FROM SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-06 23:08:53.889[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==> Parameters: 13FF28E5AFA4D417E0630100007FE3FC(String)
[2m2025-08-06 23:08:53.894[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m <==      Total: 4
[2m2025-08-06 23:08:53.895[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT a.*
        FROM SYS_MENU a
         WHERE  a.menu_id IN (
            SELECT menu_id FROM SYS_ROLE_MENU WHERE role_id IN (
            SELECT ta.role_id FROM SYS_ACCOUNT_ROLE ta LEFT JOIN sys_role tb ON ta.role_id = tb.id
            WHERE ta.account_id = ? and ta.role_id = ?
            )
            )
            
            AND a.deleted = 0 
        ORDER BY a.sort_number
[2m2025-08-06 23:08:53.900[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT a.*
        FROM SYS_MENU a
         WHERE  a.menu_id IN (
            SELECT menu_id FROM SYS_ROLE_MENU WHERE role_id IN (
            SELECT ta.role_id FROM SYS_ACCOUNT_ROLE ta LEFT JOIN sys_role tb ON ta.role_id = tb.id
            WHERE ta.account_id = ? and ta.role_id = ?
            )
            )
            
            AND a.deleted = 0 
        ORDER BY a.sort_number
[2m2025-08-06 23:08:53.902[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT a.* FROM SYS_MENU a WHERE a.menu_id IN (SELECT menu_id FROM SYS_ROLE_MENU WHERE role_id IN (SELECT ta.role_id FROM SYS_ACCOUNT_ROLE ta LEFT JOIN sys_role tb ON ta.role_id = tb.id WHERE ta.account_id = ? AND ta.role_id = ?)) AND a.deleted = 0 ORDER BY a.sort_number
[2m2025-08-06 23:08:53.902[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.s.m.R.listMenuByAccount           [0;39m [2m:[0;39m ==>  Preparing: SELECT a.* FROM SYS_MENU a WHERE a.menu_id IN (SELECT menu_id FROM SYS_ROLE_MENU WHERE role_id IN (SELECT ta.role_id FROM SYS_ACCOUNT_ROLE ta LEFT JOIN sys_role tb ON ta.role_id = tb.id WHERE ta.account_id = ? AND ta.role_id = ?)) AND a.deleted = 0 ORDER BY a.sort_number
[2m2025-08-06 23:08:53.903[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.s.m.R.listMenuByAccount           [0;39m [2m:[0;39m ==> Parameters: 13FF28E5AFA4D417E0630100007FE3FC(String), 36d5ca2b3906a97f4274ffb8e8a36210(String)
[2m2025-08-06 23:08:53.928[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.s.m.R.listMenuByAccount           [0;39m [2m:[0;39m <==      Total: 71
[2m2025-08-06 23:08:54.066[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM
[2m2025-08-06 23:08:54.086[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM
[2m2025-08-06 23:08:54.089[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM
[2m2025-08-06 23:08:54.113[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM PJ_O_SENDFORM
[2m2025-08-06 23:08:54.116[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-06 23:08:54.126[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-06 23:08:54.146[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM ORDER BY inupttime DESC ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-08-06 23:08:54.147[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: 10(Long), 0(Long)
[2m2025-08-06 23:08:54.159[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-06 23:09:02.451[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYS_ROLE
[2m2025-08-06 23:09:02.451[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    NAME,TYPE    FROM  SYS_ROLE_SCOPE
[2m2025-08-06 23:09:02.459[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    NAME,TYPE    FROM  SYS_ROLE_SCOPE
[2m2025-08-06 23:09:02.462[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT NAME, TYPE FROM SYS_ROLE_SCOPE
[2m2025-08-06 23:09:02.463[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.s.m.SysRoleScopeMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT NAME, TYPE FROM SYS_ROLE_SCOPE
[2m2025-08-06 23:09:02.463[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.s.m.SysRoleScopeMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-06 23:09:02.464[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYS_ROLE
[2m2025-08-06 23:09:02.466[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYS_ROLE
[2m2025-08-06 23:09:02.474[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.s.m.SysRoleScopeMapper.selectList [0;39m [2m:[0;39m <==      Total: 4
[2m2025-08-06 23:09:02.476[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.s.m.S.selectList_mpCount          [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM SYS_ROLE
[2m2025-08-06 23:09:02.477[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.s.m.S.selectList_mpCount          [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-06 23:09:02.482[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.s.m.S.selectList_mpCount          [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-06 23:09:02.482[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYS_ROLE ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-08-06 23:09:02.483[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 10(Long), 0(Long)
[2m2025-08-06 23:09:02.489[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m <==      Total: 4
[2m2025-08-06 23:09:17.463[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM
[2m2025-08-06 23:09:17.479[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM
[2m2025-08-06 23:09:17.481[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM
[2m2025-08-06 23:09:17.504[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM PJ_O_SENDFORM
[2m2025-08-06 23:09:17.505[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-06 23:09:17.510[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-06 23:09:17.526[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM ORDER BY inupttime DESC ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-08-06 23:09:17.527[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: 10(Long), 0(Long)
[2m2025-08-06 23:09:17.542[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-06 23:09:20.104[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS,DESCRIPTION    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-08-06 23:09:20.114[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS,DESCRIPTION    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-08-06 23:09:20.118[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS, DESCRIPTION FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-08-06 23:09:20.120[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS, DESCRIPTION FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-08-06 23:09:20.121[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-08-06 23:09:20.164[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-06 23:09:20.198[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-08-06 23:09:20.204[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-08-06 23:09:20.206[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-08-06 23:09:20.206[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-08-06 23:09:20.207[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-08-06 23:09:20.213[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-08-06 23:11:25.300[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[nexc.home:27017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Exception in monitor thread while connecting to server paynexc.home:27017

com.mongodb.MongoSocketReadException: Prematurely reached end of stream
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:184)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:824)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:517)
	at com.mongodb.internal.connection.InternalStreamConnection.receive(InternalStreamConnection.java:469)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitor.lookupServerDescription(DefaultServerMonitor.java:249)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitor.run(DefaultServerMonitor.java:176)

[2m2025-08-06 23:11:25.361[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[nexc.home:27017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Exception in monitor thread while connecting to server paynexc.home:27017

com.mongodb.MongoSocketOpenException: Exception opening socket
	at com.mongodb.internal.connection.SocketStream.lambda$open$0(SocketStream.java:85)
	at java.base/java.util.Optional.orElseThrow(Optional.java:403)
	at com.mongodb.internal.connection.SocketStream.open(SocketStream.java:85)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:233)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitor.lookupServerDescription(DefaultServerMonitor.java:219)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitor.run(DefaultServerMonitor.java:176)
Caused by: java.net.SocketException: Can't connect to SOCKS proxy:Connection refused
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:359)
	at java.base/java.net.Socket.connect(Socket.java:639)
	at com.mongodb.internal.connection.SocketStreamHelper.initialize(SocketStreamHelper.java:76)
	at com.mongodb.internal.connection.SocketStream.initializeSocket(SocketStream.java:104)
	at com.mongodb.internal.connection.SocketStream.open(SocketStream.java:79)
	... 3 common frames omitted

[2m2025-08-06 23:11:35.408[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[nexc.home:27017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Monitor thread successfully connected to server with description ServerDescription{address=paynexc.home:27017, type=STANDALONE, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=21, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=14433208, minRoundTripTimeNanos=0}
[2m2025-08-06 23:23:19.127[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS,DESCRIPTION    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-08-06 23:23:19.140[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS,DESCRIPTION    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-08-06 23:23:19.163[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS, DESCRIPTION FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-08-06 23:23:19.178[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36moracle.jdbc                             [0;39m [2m:[0;39m entering args (oracle.jdbc.internal.AbstractConnectionBuilder$1@5bbdf362)
[2m2025-08-06 23:23:19.178[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36moracle.jdbc                             [0;39m [2m:[0;39m traceId=1AAEA225. 
[2m2025-08-06 23:23:19.179[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36moracle.jdbc                             [0;39m [2m:[0;39m Session Attributes: 
sdu=8192, tdu=2097152
nt: host=paynexc.home, port=1521, socketOptions={0=YES, 1=NO, 17=0, 18=false, 2=10000, 20=true, 38=TLS, 23=40, 24=50, 40=false, 25=0}
    socket=null
client profile={oracle.net.encryption_types_client=(), oracle.net.crypto_seed=, oracle.net.authentication_services=(BEQ), oracle.net.setFIPSMode=false, oracle.net.kerberos5_mutual_authentication=false, oracle.net.encryption_client=ACCEPTED, oracle.net.crypto_checksum_client=ACCEPTED, oracle.net.crypto_checksum_types_client=()}
connection options=[host=paynexc.home port=1521 sid=ORCL protocol=TCP addr=(ADDRESS=(PROTOCOL=TCP)(HOST=paynexc.home)(PORT=1521)) conn_data=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=paynexc.home)(PORT=1521))(CONNECT_DATA=(CID=(PROGRAM=JDBC Thin Client)(HOST=__jdbc__)(USER=paynexc))(SID=ORCL))) done=true]
onBreakReset=false, dataEOF=false, negotiatedOptions=0x841, connected=false
[2m2025-08-06 23:23:19.179[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36moracle.jdbc                             [0;39m [2m:[0;39m traceId=1AAEA225, anoEnabled=true. 
[2m2025-08-06 23:23:19.179[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36moracle.jdbc                             [0;39m [2m:[0;39m Got Resend, SessionTraceId = 1AAEA225
[2m2025-08-06 23:23:19.179[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36moracle.jdbc                             [0;39m [2m:[0;39m Connection established. Cleared inet addresses in conn option and conn strategy stack
[2m2025-08-06 23:23:19.180[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36moracle.jdbc                             [0;39m [2m:[0;39m returning void
[2m2025-08-06 23:23:19.180[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36moracle.jdbc                             [0;39m [2m:[0;39m current rowPrefetch=10, tunedFetchSize=250. 
[2m2025-08-06 23:23:19.184[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36moracle.jdbc                             [0;39m [2m:[0;39m current rowPrefetch=10, tunedFetchSize=250. 
[2m2025-08-06 23:23:19.184[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36moracle.jdbc                             [0;39m [2m:[0;39m current rowPrefetch=10, tunedFetchSize=250. 
[2m2025-08-06 23:23:19.185[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36moracle.jdbc                             [0;39m [2m:[0;39m current rowPrefetch=10, tunedFetchSize=250. 
[2m2025-08-06 23:23:19.186[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36moracle.jdbc                             [0;39m [2m:[0;39m 

java.sql.SQLRecoverableException: ORA-17002: IO 错误: Broken pipe
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:978)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1205)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1135)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1472)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1343)
	at oracle.jdbc.driver.OracleStatement.executeQuery(OracleStatement.java:1727)
	at oracle.jdbc.driver.OracleStatementWrapper.executeQuery(OracleStatementWrapper.java:380)
	at com.alibaba.druid.pool.ValidConnectionCheckerAdapter.execValidQuery(ValidConnectionCheckerAdapter.java:68)
	at com.alibaba.druid.pool.vendor.OracleValidConnectionChecker.isValidConnection(OracleValidConnectionChecker.java:67)
	at com.alibaba.druid.pool.DruidAbstractDataSource.testConnectionInternal(DruidAbstractDataSource.java:1530)
	at com.alibaba.druid.pool.DruidDataSource.getConnectionDirect(DruidDataSource.java:1553)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1484)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1469)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:83)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:160)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:118)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:81)
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80)
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:348)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:89)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:64)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:336)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110)
	at jdk.internal.reflect.GeneratedMethodAccessor209.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.payne.core.handler.DecryptInterceptor.intercept(DecryptInterceptor.java:33)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy252.query(Unknown Source)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy252.query(Unknown Source)
	at jdk.internal.reflect.GeneratedMethodAccessor222.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.github.yulichang.interceptor.MPJInterceptor.intercept(MPJInterceptor.java:93)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy252.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142)
	at jdk.internal.reflect.GeneratedMethodAccessor231.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	at jdk.proxy2/jdk.proxy2.$Proxy153.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:164)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at jdk.proxy2/jdk.proxy2.$Proxy208.selectList(Unknown Source)
	at com.baomidou.mybatisplus.extension.service.IService.list(IService.java:406)
	at com.payne.server.banknote.service.impl.LabelDesignServiceImpl.getTemplateList(LabelDesignServiceImpl.java:107)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at com.payne.server.banknote.service.impl.LabelDesignServiceImpl$$SpringCGLIB$$0.getTemplateList(<generated>)
	at com.payne.server.banknote.controller.LabelDesignController.getTemplateList(LabelDesignController.java:83)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:101)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:125)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:119)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at com.payne.auth.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:117)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:87)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.ServletRequestPathFilter.doFilter(ServletRequestPathFilter.java:52)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebSecurityConfiguration.java:319)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$4(HandlerMappingIntrospector.java:267)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:240)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:90)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.io.IOException: Broken pipe
	at java.base/sun.nio.ch.FileDispatcherImpl.write0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.write(SocketDispatcher.java:62)
	at java.base/sun.nio.ch.IOUtil.writeFromNativeBuffer(IOUtil.java:132)
	at java.base/sun.nio.ch.IOUtil.write(IOUtil.java:97)
	at java.base/sun.nio.ch.IOUtil.write(IOUtil.java:53)
	at java.base/sun.nio.ch.SocketChannelImpl.write(SocketChannelImpl.java:532)
	at oracle.net.nt.TimeoutSocketChannel.writeToSocket(TimeoutSocketChannel.java:777)
	at oracle.net.nt.TimeoutSocketChannel.write(TimeoutSocketChannel.java:546)
	at oracle.net.ns.NIOPacket.writeToSocketChannel(NIOPacket.java:372)
	at oracle.net.ns.NIONSDataChannel.writeDataToSocketChannel(NIONSDataChannel.java:197)
	at oracle.jdbc.driver.T4CMAREngineNIO.flush(T4CMAREngineNIO.java:832)
	at oracle.jdbc.driver.T4CMAREngineNIO.flush(T4CMAREngineNIO.java:789)
	at oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:863)
	at oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:446)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:888)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:443)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:518)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:156)
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:963)
	... 204 common frames omitted

[2m2025-08-06 23:23:19.195[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36moracle.jdbc                             [0;39m [2m:[0;39m properties={socksProxyHost=127.0.0.1, java.specification.version=17, kotlinx.coroutines.debug.enable.mutable.state.flows.stack.trace=true, sun.arch.data.model=64, java.vendor.url=http://www.azul.com/, APPLICATION_NAME=payneServer, sun.boot.library.path=/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/lib, sun.java.command=com.payne.App, jdk.debug=release, spring.liveBeansView.mbeanDomain=, java.specification.vendor=Oracle Corporation, java.version.date=2025-04-15, java.home=/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home, rebel.env.ide.version=2025.2, java.vm.specification.vendor=Oracle Corporation, java.specification.name=Java Platform API Specification, user.script=Hans, sun.management.compiler=HotSpot 64-Bit Tiered Compilers, java.runtime.version=17.0.15+6-LTS, file.encoding=UTF-8, java.vendor.version=Zulu17.58+21-CA, java.io.tmpdir=/var/folders/q5/104n2ctj1m19xf01gllpj3240000gn/T/, java.version=17.0.15, java.vm.specification.name=Java Virtual Machine Specification, CONSOLE_LOG_CHARSET=UTF-8, native.encoding=UTF-8, java.library.path=/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:., LOCALE=zh_CN_#Hans, java.vendor=Azul Systems, Inc., DriverVersion=********.0, java.specification.maintenance.version=1, sun.io.unicode.encoding=UnicodeBig, LOGGED_APPLICATION_NAME=[payneServer] , DatabaseProductVersion=19000, https.proxyPort=6152, user.timezone=Asia/Shanghai, org.jboss.logging.provider=slf4j, java.vm.specification.version=17, os.name=Mac OS X, spring.application.admin.enabled=true, com.sun.management.jmxremote=, http.nonProxyHosts=127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|**********/10|*.**********/10|localhost|*.localhost|local|*.local|e.crashlytics.com|*.e.crashlytics.com|captive.apple.com|*.captive.apple.com|::ffff:0:0:0:0/1|*.::ffff:0:0:0:0/1|::ffff:128:0:0:0/1|*.::ffff:128:0:0:0/1|***************/32|*.***************/32, user.home=/Users/<USER>/private/var/folders/q5/104n2ctj1m19xf01gllpj3240000gn/T/tomcat.7070.15945522655021215398, os.arch=aarch64, catalina.base=/private/var/folders/q5/104n2ctj1m19xf01gllpj3240000gn/T/tomcat.7070.15945522655021215398, java.vm.info=mixed mode, emulated-client, java.class.version=61.0, http.proxyPort=6152, sun.jnu.encoding=UTF-8, rebel.env.ide.plugin.build=a6ee74ac1af075e4e2f236f32248f094315a63c4, catalina.useNaming=false, file.separator=/, java.vm.compressedOopsMode=Zero based, line.separator=
, intellij.debug.agent=true, rebel.notification.url=http://localhost:17434, user.name=paynexc, URL=****************************************, management.endpoints.jmx.exposure.include=*, jboss.modules.system.pkgs=com.intellij.rt, socksProxyPort=6153, PID=44647, kotlinx.coroutines.debug.enable.flows.stack.trace=true, java.rmi.server.randomIDs=true, debugger.agent.enable.coroutines=true, socksNonProxyHosts=127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|**********/10|*.**********/10|localhost|*.localhost|local|*.local|e.crashlytics.com|*.e.crashlytics.com|captive.apple.com|*.captive.apple.com|::ffff:0:0:0:0/1|*.::ffff:0:0:0:0/1|::ffff:128:0:0:0/1|*.::ffff:128:0:0:0/1|***************/32|*.***************/32, http.proxyHost=127.0.0.1, java.class.path=/Users/<USER>/project/ele-admin/dev-platform/payne-server/target/classes:/Users/<USER>/project/ele-admin/dev-platform/payne-auth/target/classes:/Users/<USER>/project/ele-admin/dev-platform/payne-common/payne-common-core/target/classes:/Users/<USER>/.m2/repository/org/redisson/redisson-spring-boot-starter/3.36.0/redisson-spring-boot-starter-3.36.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/3.5.3/spring-boot-starter-actuator-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/3.5.3/spring-boot-actuator-autoconfigure-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/3.5.3/spring-boot-actuator-3.5.3.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-jakarta9/1.15.1/micrometer-jakarta9-1.15.1.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.15.1/micrometer-core-1.15.1.jar:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.2.2/HdrHistogram-2.2.2.jar:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-redis/3.5.3/spring-boot-starter-data-redis-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/3.5.1/spring-data-redis-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/3.5.1/spring-data-keyvalue-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/6.2.8/spring-oxm-6.2.8.jar:/Users/<USER>/.m2/repository/org/redisson/redisson/3.36.0/redisson-3.36.0.jar:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.122.Final/netty-common-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.122.Final/netty-codec-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.122.Final/netty-buffer-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.122.Final/netty-transport-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.122.Final/netty-resolver-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns/4.1.122.Final/netty-resolver-dns-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-dns/4.1.122.Final/netty-codec-dns-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.122.Final/netty-handler-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-unix-common/4.1.122.Final/netty-transport-native-unix-common-4.1.122.Final.jar:/Users/<USER>/.m2/repository/javax/cache/cache-api/1.1.1/cache-api-1.1.1.jar:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.7.7/reactor-core-3.7.7.jar:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar:/Users/<USER>/.m2/repository/io/reactivex/rxjava3/rxjava/3.1.10/rxjava-3.1.10.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/kryo/5.6.0/kryo-5.6.0.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/reflectasm/1.11.9/reflectasm-1.11.9.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/minlog/1.3.1/minlog-1.3.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.19.1/jackson-dataformat-yaml-2.19.1.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.17.6/byte-buddy-1.17.6.jar:/Users/<USER>/.m2/repository/org/jodd/jodd-util/6.2.2/jodd-util-6.2.2.jar:/Users/<USER>/.m2/repository/org/redisson/redisson-spring-data-33/3.36.0/redisson-spring-data-33-3.36.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-security/3.5.3/spring-boot-starter-security-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-config/6.5.1/spring-security-config-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-core/6.5.1/spring-security-core-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-crypto/6.5.1/spring-security-crypto-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-web/6.5.1/spring-security-web-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-mongodb/3.5.3/spring-boot-starter-data-mongodb-3.5.3.jar:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-sync/5.5.1/mongodb-driver-sync-5.5.1.jar:/Users/<USER>/.m2/repository/org/mongodb/bson/5.5.1/bson-5.5.1.jar:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-core/5.5.1/mongodb-driver-core-5.5.1.jar:/Users/<USER>/.m2/repository/org/mongodb/bson-record-codec/5.5.1/bson-record-codec-5.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-mongodb/4.5.1/spring-data-mongodb-4.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/6.2.8/spring-tx-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/3.5.1/spring-data-commons-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-cache/3.5.3/spring-boot-starter-cache-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/6.2.8/spring-context-support-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/3.5.3/spring-boot-starter-validation-3.5.3.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/8.0.2.Final/hibernate-validator-8.0.2.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/3.0.2/jakarta.validation-api-3.0.2.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.6.1.Final/jboss-logging-3.6.1.Final.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.7.0/classmate-1.7.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-jpa/3.5.3/spring-boot-starter-data-jpa-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/3.5.3/spring-boot-starter-jdbc-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/6.2.8/spring-jdbc-6.2.8.jar:/Users/<USER>/.m2/repository/org/hibernate/orm/hibernate-core/6.6.18.Final/hibernate-core-6.6.18.Final.jar:/Users/<USER>/.m2/repository/jakarta/persistence/jakarta.persistence-api/3.1.0/jakarta.persistence-api-3.1.0.jar:/Users/<USER>/.m2/repository/jakarta/transaction/jakarta.transaction-api/2.0.1/jakarta.transaction-api-2.0.1.jar:/Users/<USER>/.m2/repository/org/hibernate/common/hibernate-commons-annotations/7.0.3.Final/hibernate-commons-annotations-7.0.3.Final.jar:/Users/<USER>/.m2/repository/io/smallrye/jandex/3.2.0/jandex-3.2.0.jar:/Users/<USER>/.m2/repository/jakarta/inject/jakarta.inject-api/2.0.1/jakarta.inject-api-2.0.1.jar:/Users/<USER>/.m2/repository/org/antlr/antlr4-runtime/4.13.0/antlr4-runtime-4.13.0.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-jpa/3.5.1/spring-data-jpa-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-orm/6.2.8/spring-orm-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aspects/6.2.8/spring-aspects-6.2.8.jar:/Users/<USER>/.m2/repository/com/alibaba/druid-spring-boot-starter/1.2.21/druid-spring-boot-starter-1.2.21.jar:/Users/<USER>/.m2/repository/com/alibaba/druid/1.2.21/druid-1.2.21.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-runtime/4.0.5/jaxb-runtime-4.0.5.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-core/4.0.5/jaxb-core-4.0.5.jar:/Users/<USER>/.m2/repository/org/eclipse/angus/angus-activation/2.0.2/angus-activation-2.0.2.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/txw2/4.0.5/txw2-4.0.5.jar:/Users/<USER>/.m2/repository/com/sun/istack/istack-commons-runtime/4.1.2/istack-commons-runtime-4.1.2.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot3-starter/3.5.5/mybatis-plus-spring-boot3-starter-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.5.5/mybatis-plus-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.5.5/mybatis-plus-core-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.5.5/mybatis-plus-annotation-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.5.5/mybatis-plus-extension-3.5.5.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.15/mybatis-3.5.15.jar:/Users/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/4.6/jsqlparser-4.6.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/3.0.3/mybatis-spring-3.0.3.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot-autoconfigure/3.5.5/mybatis-plus-spring-boot-autoconfigure-3.5.5.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-boot-starter/1.4.12/mybatis-plus-join-boot-starter-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-extension/1.4.12/mybatis-plus-join-extension-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-core/1.4.12/mybatis-plus-join-core-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-annotation/1.4.12/mybatis-plus-join-annotation-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v33x/1.4.12/mybatis-plus-join-adapter-v33x-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-base/1.4.12/mybatis-plus-join-adapter-base-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-jsqlparser/1.4.12/mybatis-plus-join-adapter-jsqlparser-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-jsqlparser-v46/1.4.12/mybatis-plus-join-adapter-jsqlparser-v46-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v3431/1.4.12/mybatis-plus-join-adapter-v3431-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v352/1.4.12/mybatis-plus-join-adapter-v352-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v355/1.4.12/mybatis-plus-join-adapter-v355-1.4.12.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-aop/3.5.3/spring-boot-starter-aop-3.5.3.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.24/aspectjweaver-1.9.24.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.38/lombok-1.18.38.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-all/5.8.26/hutool-all-5.8.26.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.17.0/commons-lang3-3.17.0.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.5.18/logback-classic-1.5.18.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.5.18/logback-core-1.5.18.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/31.1-jre/guava-31.1-jre.jar:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.12.0/checker-qual-3.12.0.jar:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.11.0/error_prone_annotations-2.11.0.jar:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/1.3/j2objc-annotations-1.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-properties/2.19.1/jackson-dataformat-properties-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.19.1/jackson-core-2.19.1.jar:/Users/<USER>/.m2/repository/com/dameng/DmJdbcDriver18/8.1.2.79/DmJdbcDriver18-8.1.2.79.jar:/Users/<USER>/.m2/repository/com/pig4cloud/excel/excel-spring-boot-starter/3.2.1/excel-spring-boot-starter-3.2.1.jar:/Users/<USER>/.m2/repository/com/alibaba/easyexcel/3.3.4/easyexcel-3.3.4.jar:/Users/<USER>/.m2/repository/com/alibaba/easyexcel-core/3.3.4/easyexcel-core-3.3.4.jar:/Users/<USER>/.m2/repository/com/alibaba/easyexcel-support/3.3.4/easyexcel-support-3.3.4.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi/4.1.2/poi-4.1.2.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.4/commons-collections4-4.4.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-math3/3.6.1/commons-math3-3.6.1.jar:/Users/<USER>/.m2/repository/com/zaxxer/SparseBitSet/1.2/SparseBitSet-1.2.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/4.1.2/poi-ooxml-4.1.2.jar:/Users/<USER>/.m2/repository/com/github/virtuald/curvesapi/1.06/curvesapi-1.06.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-csv/1.8/commons-csv-1.8.jar:/Users/<USER>/.m2/repository/org/ehcache/ehcache/3.10.8/ehcache-3.10.8.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.26.1/commons-compress-1.26.1.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.18.0/commons-codec-1.18.0.jar:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/3.0/hamcrest-core-3.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-test/3.5.3/spring-boot-starter-test-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/3.5.3/spring-boot-test-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test-autoconfigure/3.5.3/spring-boot-test-autoconfigure-3.5.3.jar:/Users/<USER>/.m2/repository/com/jayway/jsonpath/json-path/2.9.0/json-path-2.9.0.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/4.0.2/jakarta.xml.bind-api-4.0.2.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/2.1.3/jakarta.activation-api-2.1.3.jar:/Users/<USER>/.m2/repository/net/minidev/json-smart/2.5.2/json-smart-2.5.2.jar:/Users/<USER>/.m2/repository/net/minidev/accessors-smart/2.5.2/accessors-smart-2.5.2.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.27.3/assertj-core-3.27.3.jar:/Users/<USER>/.m2/repository/org/awaitility/awaitility/4.2.2/awaitility-4.2.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/3.0/hamcrest-3.0.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter/5.12.2/junit-jupiter-5.12.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-api/5.12.2/junit-jupiter-api-5.12.2.jar:/Users/<USER>/.m2/repository/org/opentest4j/opentest4j/1.3.0/opentest4j-1.3.0.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-commons/1.12.2/junit-platform-commons-1.12.2.jar:/Users/<USER>/.m2/repository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-params/5.12.2/junit-jupiter-params-5.12.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-engine/5.12.2/junit-jupiter-engine-5.12.2.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-engine/1.12.2/junit-platform-engine-1.12.2.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-core/5.17.0/mockito-core-5.17.0.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.17.6/byte-buddy-agent-1.17.6.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.3/objenesis-3.3.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-junit-jupiter/5.17.0/mockito-junit-jupiter-5.17.0.jar:/Users/<USER>/.m2/repository/org/skyscreamer/jsonassert/1.5.3/jsonassert-1.5.3.jar:/Users/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-test/6.2.8/spring-test-6.2.8.jar:/Users/<USER>/.m2/repository/org/xmlunit/xmlunit-core/2.10.2/xmlunit-core-2.10.2.jar:/Users/<USER>/.m2/repository/com/github/whvcse/easy-captcha/1.6.2/easy-captcha-1.6.2.jar:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk18on/1.76/bcprov-jdk18on-1.76.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-impl/0.11.2/jjwt-impl-0.11.2.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-api/0.11.2/jjwt-api-0.11.2.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-jackson/0.11.2/jjwt-jackson-0.11.2.jar:/Users/<USER>/.m2/repository/cglib/cglib/3.3.0/cglib-3.3.0.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm/7.1/asm-7.1.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-text/1.13.1/commons-text-1.13.1.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.16.1/commons-io-2.16.1.jar:/Users/<USER>/.m2/repository/org/codehaus/janino/janino/3.1.12/janino-3.1.12.jar:/Users/<USER>/.m2/repository/org/codehaus/janino/commons-compiler/3.1.12/commons-compiler-3.1.12.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-websocket/3.5.3/spring-boot-starter-websocket-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-messaging/6.2.8/spring-messaging-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-websocket/6.2.8/spring-websocket-6.2.8.jar:/Users/<USER>/.m2/repository/org/java-websocket/Java-WebSocket/1.5.2/Java-WebSocket-1.5.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-rsocket/3.5.3/spring-boot-starter-rsocket-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-reactor-netty/3.5.3/spring-boot-starter-reactor-netty-3.5.3.jar:/Users/<USER>/.m2/repository/io/projectreactor/netty/reactor-netty-http/1.2.7/reactor-netty-http-1.2.7.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-http/4.1.122.Final/netty-codec-http-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-http2/4.1.122.Final/netty-codec-http2-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns-native-macos/4.1.122.Final/netty-resolver-dns-native-macos-4.1.122.Final-osx-x86_64.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns-classes-macos/4.1.122.Final/netty-resolver-dns-classes-macos-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-epoll/4.1.122.Final/netty-transport-native-epoll-4.1.122.Final-linux-x86_64.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-classes-epoll/4.1.122.Final/netty-transport-classes-epoll-4.1.122.Final.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-cbor/2.19.1/jackson-dataformat-cbor-2.19.1.jar:/Users/<USER>/.m2/repository/io/rsocket/rsocket-core/1.1.5/rsocket-core-1.1.5.jar:/Users/<USER>/.m2/repository/io/rsocket/rsocket-transport-netty/1.1.5/rsocket-transport-netty-1.1.5.jar:/Users/<USER>/.m2/repository/io/projectreactor/netty/reactor-netty-core/1.2.7/reactor-netty-core-1.2.7.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler-proxy/4.1.122.Final/netty-handler-proxy-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-socks/4.1.122.Final/netty-codec-socks-4.1.122.Final.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-thymeleaf/3.5.3/spring-boot-starter-thymeleaf-3.5.3.jar:/Users/<USER>/.m2/repository/org/thymeleaf/thymeleaf-spring6/3.1.3.RELEASE/thymeleaf-spring6-3.1.3.RELEASE.jar:/Users/<USER>/.m2/repository/org/thymeleaf/thymeleaf/3.1.3.RELEASE/thymeleaf-3.1.3.RELEASE.jar:/Users/<USER>/.m2/repository/org/attoparser/attoparser/2.0.7.RELEASE/attoparser-2.0.7.RELEASE.jar:/Users/<USER>/.m2/repository/org/unbescape/unbescape/1.1.6.RELEASE/unbescape-1.1.6.RELEASE.jar:/Users/<USER>/.m2/repository/com/payne/payne-tools/1.0.1/payne-tools-1.0.1.jar:/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes:/Users/<USER>/.m2/repository/com/sun/mail/javax.mail/1.6.2/javax.mail-1.6.2.jar:/Users/<USER>/.m2/repository/javax/activation/activation/1.1/activation-1.1.jar:/Users/<USER>/project/ele-admin/dev-platform/payne-generator/target/classes:/Users/<USER>/.m2/repository/org/apache/velocity/velocity-engine-core/2.3/velocity-engine-core-2.3.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar:/Users/<USER>/.m2/repository/com/oracle/ojdbc8/********.0/ojdbc8-********.0.jar:/Users/<USER>/.m2/repository/com/mysql/mysql-connector-j/8.0.33/mysql-connector-j-8.0.33.jar:/Users/<USER>/.m2/repository/com/google/protobuf/protobuf-java/3.21.9/protobuf-java-3.21.9.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/6.3.0/HikariCP-6.3.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-devtools/3.5.3/spring-boot-devtools-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/3.5.3/spring-boot-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/6.2.8/spring-core-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/6.2.8/spring-jcl-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/6.2.8/spring-context-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/3.5.3/spring-boot-autoconfigure-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/3.5.3/spring-boot-starter-web-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/3.5.3/spring-boot-starter-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/3.5.3/spring-boot-starter-logging-3.5.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.24.3/log4j-to-slf4j-2.24.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.24.3/log4j-api-2.24.3.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/2.0.17/jul-to-slf4j-2.0.17.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/2.1/snakeyaml-2.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/3.5.3/spring-boot-starter-json-3.5.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.19.1/jackson-databind-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.19.1/jackson-annotations-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.19.1/jackson-datatype-jdk8-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.19.1/jackson-datatype-jsr310-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.19.1/jackson-module-parameter-names-2.19.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/3.5.3/spring-boot-starter-tomcat-3.5.3.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/10.1.42/tomcat-embed-core-10.1.42.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/10.1.42/tomcat-embed-el-10.1.42.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/10.1.42/tomcat-embed-websocket-10.1.42.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/6.2.8/spring-web-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/6.2.8/spring-beans-6.2.8.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.15.1/micrometer-observation-1.15.1.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.15.1/micrometer-commons-1.15.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/6.2.8/spring-webmvc-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/6.2.8/spring-aop-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/6.2.8/spring-expression-6.2.8.jar:/Users/<USER>/Applications/IntelliJ IDEA Ultimate.app/Contents/lib/idea_rt.jar, java.vm.vendor=Azul Systems, Inc., sun.java.launcher=SUN_STANDARD, user.country=CN, sun.cpu.endian=little, user.language=zh, https.proxyHost=127.0.0.1, ftp.nonProxyHosts=127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|**********/10|*.**********/10|localhost|*.localhost|local|*.local|e.crashlytics.com|*.e.crashlytics.com|captive.apple.com|*.captive.apple.com|::ffff:0:0:0:0/1|*.::ffff:0:0:0:0/1|::ffff:128:0:0:0/1|*.::ffff:128:0:0:0/1|***************/32|*.***************/32, spring.jmx.enabled=true, rebel.env.ide.plugin.version=2025.3.1, java.runtime.name=OpenJDK Runtime Environment, rebel.native.image=/private/var/folders/q5/104n2ctj1m19xf01gllpj3240000gn/T/jrebel-JRebel-202508011409/lib/libjrebel64.dylib, rebel.env.ide=intellij, rebel.plugins=/Users/<USER>/Library/Application Support/JetBrains/IntelliJIdea2025.2/plugins/jr-mp-ide-idea/lib/jr-mybatisplus-1.0.7.jar, java.vendor.url.bug=http://www.azul.com/support/, user.dir=/Users/<USER>/project/ele-admin, java.vm.version=17.0.15+6-LTS, rebel.base=/Users/<USER>/.jrebel, rebel.env.ide.product=IU}. 
[2m2025-08-06 23:23:19.196[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36moracle.jdbc                             [0;39m [2m:[0;39m entering args (oracle.jdbc.internal.AbstractConnectionBuilder$1@722b45e1)
[2m2025-08-06 23:23:19.197[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36moracle.jdbc                             [0;39m [2m:[0;39m traceId=6164D0C7. 
[2m2025-08-06 23:23:19.197[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36moracle.jdbc                             [0;39m [2m:[0;39m Session Attributes: 
sdu=8192, tdu=2097152
nt: host=paynexc.home, port=1521, socketOptions={0=YES, 1=NO, 17=0, 18=false, 2=10000, 20=true, 38=TLS, 23=40, 24=50, 40=false, 25=0}
    socket=null
client profile={oracle.net.encryption_types_client=(), oracle.net.crypto_seed=, oracle.net.authentication_services=(BEQ), oracle.net.setFIPSMode=false, oracle.net.kerberos5_mutual_authentication=false, oracle.net.encryption_client=ACCEPTED, oracle.net.crypto_checksum_client=ACCEPTED, oracle.net.crypto_checksum_types_client=()}
connection options=[host=paynexc.home port=1521 sid=ORCL protocol=TCP addr=(ADDRESS=(PROTOCOL=TCP)(HOST=paynexc.home)(PORT=1521)) conn_data=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=paynexc.home)(PORT=1521))(CONNECT_DATA=(CID=(PROGRAM=JDBC Thin Client)(HOST=__jdbc__)(USER=paynexc))(SID=ORCL))) done=true]
onBreakReset=false, dataEOF=false, negotiatedOptions=0x841, connected=false
[2m2025-08-06 23:23:19.197[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36moracle.jdbc                             [0;39m [2m:[0;39m traceId=6164D0C7, anoEnabled=true. 
[2m2025-08-06 23:23:19.197[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36moracle.jdbc                             [0;39m [2m:[0;39m Got Resend, SessionTraceId = 6164D0C7
[2m2025-08-06 23:23:19.197[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36moracle.jdbc                             [0;39m [2m:[0;39m Connection established. Cleared inet addresses in conn option and conn strategy stack
[2m2025-08-06 23:23:19.197[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36moracle.jdbc                             [0;39m [2m:[0;39m returning void
[2m2025-08-06 23:23:19.198[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36moracle.jdbc                             [0;39m [2m:[0;39m Break received from server. Responding with reset...
[2m2025-08-06 23:23:19.198[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36moracle.jdbc                             [0;39m [2m:[0;39m SO_TIMEOUT interrupt timer cancelled null
[2m2025-08-06 23:23:19.198[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36moracle.jdbc                             [0;39m [2m:[0;39m Sending break marker, SessionTraceId = 6164D0C7
[2m2025-08-06 23:23:19.198[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36moracle.jdbc                             [0;39m [2m:[0;39m 

java.sql.SQLSyntaxErrorException: ORA-00910: 指定的长度对于数据类型而言过长

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:702)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:608)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1248)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:1041)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:443)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:518)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:156)
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:963)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1205)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1135)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1472)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1343)
	at oracle.jdbc.driver.OracleStatement.executeQuery(OracleStatement.java:1727)
	at oracle.jdbc.driver.OracleStatementWrapper.executeQuery(OracleStatementWrapper.java:380)
	at org.hibernate.dialect.OracleServerConfiguration.fromDialectResolutionInfo(OracleServerConfiguration.java:139)
	at org.hibernate.dialect.OracleDialect.<init>(OracleDialect.java:213)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)
	at org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.lambda$constructDialect$0(DialectFactoryImpl.java:131)
	at org.hibernate.boot.registry.selector.internal.StrategySelectorImpl.resolveStrategy(StrategySelectorImpl.java:240)
	at org.hibernate.boot.registry.selector.internal.StrategySelectorImpl.resolveStrategy(StrategySelectorImpl.java:189)
	at org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.constructDialect(DialectFactoryImpl.java:123)
	at org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.buildDialect(DialectFactoryImpl.java:88)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$1.execute(JdbcEnvironmentInitiator.java:369)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$1.execute(JdbcEnvironmentInitiator.java:337)
	at org.hibernate.jdbc.WorkExecutor.executeReturningWork(WorkExecutor.java:58)
	at org.hibernate.jdbc.AbstractReturningWork.accept(AbstractReturningWork.java:34)
	at org.hibernate.resource.transaction.backend.jdbc.internal.JdbcIsolationDelegate.delegateWork(JdbcIsolationDelegate.java:70)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.getJdbcEnvironmentUsingJdbcMetadata(JdbcEnvironmentInitiator.java:336)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:129)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:81)
	at org.hibernate.boot.registry.internal.StandardServiceRegistryImpl.initiateService(StandardServiceRegistryImpl.java:130)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.createService(AbstractServiceRegistryImpl.java:263)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:238)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:215)
	at org.hibernate.boot.model.relational.Database.<init>(Database.java:45)
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.getDatabase(InFlightMetadataCollectorImpl.java:226)
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.<init>(InFlightMetadataCollectorImpl.java:194)
	at org.hibernate.boot.model.process.spi.MetadataBuildingProcess.complete(MetadataBuildingProcess.java:171)
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.metadata(EntityManagerFactoryBuilderImpl.java:1442)
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.__build(EntityManagerFactoryBuilderImpl.java:1513)
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:40002)
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.__createEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:66)
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:40003)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.__createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:390)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:46002)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:419)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:400)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:366)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1873)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1822)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:607)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:207)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:970)
	at org.springframework.context.support.AbstractApplicationContext.__refresh(AbstractApplicationContext.java:627)
	at org.springframework.context.support.AbstractApplicationContext.jrLockAndRefresh(AbstractApplicationContext.java:41002)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:42008)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at com.payne.App.main(App.java:19)
Caused by: oracle.jdbc.OracleDatabaseException: ORA-00910: 指定的长度对于数据类型而言过长

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:710)
	... 70 common frames omitted

[2m2025-08-06 23:23:19.200[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36moracle.jdbc                             [0;39m [2m:[0;39m current rowPrefetch=10, tunedFetchSize=250. 
[2m2025-08-06 23:23:19.200[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36moracle.jdbc                             [0;39m [2m:[0;39m 

java.sql.SQLRecoverableException: ORA-17002: IO 错误: Broken pipe
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:978)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1205)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1135)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1472)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1343)
	at oracle.jdbc.driver.OracleStatement.executeQuery(OracleStatement.java:1727)
	at oracle.jdbc.driver.OracleStatementWrapper.executeQuery(OracleStatementWrapper.java:380)
	at com.alibaba.druid.pool.ValidConnectionCheckerAdapter.execValidQuery(ValidConnectionCheckerAdapter.java:68)
	at com.alibaba.druid.pool.vendor.OracleValidConnectionChecker.isValidConnection(OracleValidConnectionChecker.java:67)
	at com.alibaba.druid.pool.DruidAbstractDataSource.testConnectionInternal(DruidAbstractDataSource.java:1530)
	at com.alibaba.druid.pool.DruidDataSource.getConnectionDirect(DruidDataSource.java:1553)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1484)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1469)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:83)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:160)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:118)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:81)
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80)
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:348)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:89)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:64)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:336)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110)
	at jdk.internal.reflect.GeneratedMethodAccessor209.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.payne.core.handler.DecryptInterceptor.intercept(DecryptInterceptor.java:33)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy252.query(Unknown Source)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy252.query(Unknown Source)
	at jdk.internal.reflect.GeneratedMethodAccessor222.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.github.yulichang.interceptor.MPJInterceptor.intercept(MPJInterceptor.java:93)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy252.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142)
	at jdk.internal.reflect.GeneratedMethodAccessor231.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	at jdk.proxy2/jdk.proxy2.$Proxy153.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:164)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at jdk.proxy2/jdk.proxy2.$Proxy208.selectList(Unknown Source)
	at com.baomidou.mybatisplus.extension.service.IService.list(IService.java:406)
	at com.payne.server.banknote.service.impl.LabelDesignServiceImpl.getTemplateList(LabelDesignServiceImpl.java:107)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at com.payne.server.banknote.service.impl.LabelDesignServiceImpl$$SpringCGLIB$$0.getTemplateList(<generated>)
	at com.payne.server.banknote.controller.LabelDesignController.getTemplateList(LabelDesignController.java:83)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:101)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:125)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:119)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at com.payne.auth.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:117)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:87)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.ServletRequestPathFilter.doFilter(ServletRequestPathFilter.java:52)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebSecurityConfiguration.java:319)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$4(HandlerMappingIntrospector.java:267)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:240)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:90)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.io.IOException: Broken pipe
	at java.base/sun.nio.ch.FileDispatcherImpl.write0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.write(SocketDispatcher.java:62)
	at java.base/sun.nio.ch.IOUtil.writeFromNativeBuffer(IOUtil.java:132)
	at java.base/sun.nio.ch.IOUtil.write(IOUtil.java:97)
	at java.base/sun.nio.ch.IOUtil.write(IOUtil.java:53)
	at java.base/sun.nio.ch.SocketChannelImpl.write(SocketChannelImpl.java:532)
	at oracle.net.nt.TimeoutSocketChannel.writeToSocket(TimeoutSocketChannel.java:777)
	at oracle.net.nt.TimeoutSocketChannel.write(TimeoutSocketChannel.java:546)
	at oracle.net.ns.NIOPacket.writeToSocketChannel(NIOPacket.java:372)
	at oracle.net.ns.NIONSDataChannel.writeDataToSocketChannel(NIONSDataChannel.java:197)
	at oracle.jdbc.driver.T4CMAREngineNIO.flush(T4CMAREngineNIO.java:832)
	at oracle.jdbc.driver.T4CMAREngineNIO.flush(T4CMAREngineNIO.java:789)
	at oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:863)
	at oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:446)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:888)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:443)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:518)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:156)
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:963)
	... 204 common frames omitted

[2m2025-08-06 23:23:19.219[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36moracle.jdbc                             [0;39m [2m:[0;39m properties={socksProxyHost=127.0.0.1, java.specification.version=17, kotlinx.coroutines.debug.enable.mutable.state.flows.stack.trace=true, sun.arch.data.model=64, java.vendor.url=http://www.azul.com/, APPLICATION_NAME=payneServer, sun.boot.library.path=/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/lib, sun.java.command=com.payne.App, jdk.debug=release, spring.liveBeansView.mbeanDomain=, java.specification.vendor=Oracle Corporation, java.version.date=2025-04-15, java.home=/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home, rebel.env.ide.version=2025.2, java.vm.specification.vendor=Oracle Corporation, java.specification.name=Java Platform API Specification, user.script=Hans, sun.management.compiler=HotSpot 64-Bit Tiered Compilers, java.runtime.version=17.0.15+6-LTS, file.encoding=UTF-8, java.vendor.version=Zulu17.58+21-CA, java.io.tmpdir=/var/folders/q5/104n2ctj1m19xf01gllpj3240000gn/T/, java.version=17.0.15, java.vm.specification.name=Java Virtual Machine Specification, CONSOLE_LOG_CHARSET=UTF-8, native.encoding=UTF-8, java.library.path=/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:., LOCALE=zh_CN_#Hans, java.vendor=Azul Systems, Inc., DriverVersion=********.0, java.specification.maintenance.version=1, sun.io.unicode.encoding=UnicodeBig, LOGGED_APPLICATION_NAME=[payneServer] , DatabaseProductVersion=19000, https.proxyPort=6152, user.timezone=Asia/Shanghai, org.jboss.logging.provider=slf4j, java.vm.specification.version=17, os.name=Mac OS X, spring.application.admin.enabled=true, com.sun.management.jmxremote=, http.nonProxyHosts=127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|**********/10|*.**********/10|localhost|*.localhost|local|*.local|e.crashlytics.com|*.e.crashlytics.com|captive.apple.com|*.captive.apple.com|::ffff:0:0:0:0/1|*.::ffff:0:0:0:0/1|::ffff:128:0:0:0/1|*.::ffff:128:0:0:0/1|***************/32|*.***************/32, user.home=/Users/<USER>/private/var/folders/q5/104n2ctj1m19xf01gllpj3240000gn/T/tomcat.7070.15945522655021215398, os.arch=aarch64, catalina.base=/private/var/folders/q5/104n2ctj1m19xf01gllpj3240000gn/T/tomcat.7070.15945522655021215398, java.vm.info=mixed mode, emulated-client, java.class.version=61.0, http.proxyPort=6152, sun.jnu.encoding=UTF-8, rebel.env.ide.plugin.build=a6ee74ac1af075e4e2f236f32248f094315a63c4, catalina.useNaming=false, file.separator=/, java.vm.compressedOopsMode=Zero based, line.separator=
, intellij.debug.agent=true, rebel.notification.url=http://localhost:17434, user.name=paynexc, URL=****************************************, management.endpoints.jmx.exposure.include=*, jboss.modules.system.pkgs=com.intellij.rt, socksProxyPort=6153, PID=44647, kotlinx.coroutines.debug.enable.flows.stack.trace=true, java.rmi.server.randomIDs=true, debugger.agent.enable.coroutines=true, socksNonProxyHosts=127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|**********/10|*.**********/10|localhost|*.localhost|local|*.local|e.crashlytics.com|*.e.crashlytics.com|captive.apple.com|*.captive.apple.com|::ffff:0:0:0:0/1|*.::ffff:0:0:0:0/1|::ffff:128:0:0:0/1|*.::ffff:128:0:0:0/1|***************/32|*.***************/32, http.proxyHost=127.0.0.1, java.class.path=/Users/<USER>/project/ele-admin/dev-platform/payne-server/target/classes:/Users/<USER>/project/ele-admin/dev-platform/payne-auth/target/classes:/Users/<USER>/project/ele-admin/dev-platform/payne-common/payne-common-core/target/classes:/Users/<USER>/.m2/repository/org/redisson/redisson-spring-boot-starter/3.36.0/redisson-spring-boot-starter-3.36.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/3.5.3/spring-boot-starter-actuator-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/3.5.3/spring-boot-actuator-autoconfigure-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/3.5.3/spring-boot-actuator-3.5.3.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-jakarta9/1.15.1/micrometer-jakarta9-1.15.1.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.15.1/micrometer-core-1.15.1.jar:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.2.2/HdrHistogram-2.2.2.jar:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-redis/3.5.3/spring-boot-starter-data-redis-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/3.5.1/spring-data-redis-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/3.5.1/spring-data-keyvalue-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/6.2.8/spring-oxm-6.2.8.jar:/Users/<USER>/.m2/repository/org/redisson/redisson/3.36.0/redisson-3.36.0.jar:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.122.Final/netty-common-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.122.Final/netty-codec-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.122.Final/netty-buffer-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.122.Final/netty-transport-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.122.Final/netty-resolver-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns/4.1.122.Final/netty-resolver-dns-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-dns/4.1.122.Final/netty-codec-dns-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.122.Final/netty-handler-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-unix-common/4.1.122.Final/netty-transport-native-unix-common-4.1.122.Final.jar:/Users/<USER>/.m2/repository/javax/cache/cache-api/1.1.1/cache-api-1.1.1.jar:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.7.7/reactor-core-3.7.7.jar:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar:/Users/<USER>/.m2/repository/io/reactivex/rxjava3/rxjava/3.1.10/rxjava-3.1.10.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/kryo/5.6.0/kryo-5.6.0.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/reflectasm/1.11.9/reflectasm-1.11.9.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/minlog/1.3.1/minlog-1.3.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.19.1/jackson-dataformat-yaml-2.19.1.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.17.6/byte-buddy-1.17.6.jar:/Users/<USER>/.m2/repository/org/jodd/jodd-util/6.2.2/jodd-util-6.2.2.jar:/Users/<USER>/.m2/repository/org/redisson/redisson-spring-data-33/3.36.0/redisson-spring-data-33-3.36.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-security/3.5.3/spring-boot-starter-security-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-config/6.5.1/spring-security-config-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-core/6.5.1/spring-security-core-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-crypto/6.5.1/spring-security-crypto-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-web/6.5.1/spring-security-web-6.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-mongodb/3.5.3/spring-boot-starter-data-mongodb-3.5.3.jar:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-sync/5.5.1/mongodb-driver-sync-5.5.1.jar:/Users/<USER>/.m2/repository/org/mongodb/bson/5.5.1/bson-5.5.1.jar:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-core/5.5.1/mongodb-driver-core-5.5.1.jar:/Users/<USER>/.m2/repository/org/mongodb/bson-record-codec/5.5.1/bson-record-codec-5.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-mongodb/4.5.1/spring-data-mongodb-4.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/6.2.8/spring-tx-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/3.5.1/spring-data-commons-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-cache/3.5.3/spring-boot-starter-cache-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/6.2.8/spring-context-support-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/3.5.3/spring-boot-starter-validation-3.5.3.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/8.0.2.Final/hibernate-validator-8.0.2.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/3.0.2/jakarta.validation-api-3.0.2.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.6.1.Final/jboss-logging-3.6.1.Final.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.7.0/classmate-1.7.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-jpa/3.5.3/spring-boot-starter-data-jpa-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/3.5.3/spring-boot-starter-jdbc-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/6.2.8/spring-jdbc-6.2.8.jar:/Users/<USER>/.m2/repository/org/hibernate/orm/hibernate-core/6.6.18.Final/hibernate-core-6.6.18.Final.jar:/Users/<USER>/.m2/repository/jakarta/persistence/jakarta.persistence-api/3.1.0/jakarta.persistence-api-3.1.0.jar:/Users/<USER>/.m2/repository/jakarta/transaction/jakarta.transaction-api/2.0.1/jakarta.transaction-api-2.0.1.jar:/Users/<USER>/.m2/repository/org/hibernate/common/hibernate-commons-annotations/7.0.3.Final/hibernate-commons-annotations-7.0.3.Final.jar:/Users/<USER>/.m2/repository/io/smallrye/jandex/3.2.0/jandex-3.2.0.jar:/Users/<USER>/.m2/repository/jakarta/inject/jakarta.inject-api/2.0.1/jakarta.inject-api-2.0.1.jar:/Users/<USER>/.m2/repository/org/antlr/antlr4-runtime/4.13.0/antlr4-runtime-4.13.0.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-jpa/3.5.1/spring-data-jpa-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-orm/6.2.8/spring-orm-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aspects/6.2.8/spring-aspects-6.2.8.jar:/Users/<USER>/.m2/repository/com/alibaba/druid-spring-boot-starter/1.2.21/druid-spring-boot-starter-1.2.21.jar:/Users/<USER>/.m2/repository/com/alibaba/druid/1.2.21/druid-1.2.21.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-runtime/4.0.5/jaxb-runtime-4.0.5.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-core/4.0.5/jaxb-core-4.0.5.jar:/Users/<USER>/.m2/repository/org/eclipse/angus/angus-activation/2.0.2/angus-activation-2.0.2.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/txw2/4.0.5/txw2-4.0.5.jar:/Users/<USER>/.m2/repository/com/sun/istack/istack-commons-runtime/4.1.2/istack-commons-runtime-4.1.2.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot3-starter/3.5.5/mybatis-plus-spring-boot3-starter-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.5.5/mybatis-plus-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.5.5/mybatis-plus-core-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.5.5/mybatis-plus-annotation-3.5.5.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.5.5/mybatis-plus-extension-3.5.5.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.15/mybatis-3.5.15.jar:/Users/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/4.6/jsqlparser-4.6.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/3.0.3/mybatis-spring-3.0.3.jar:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot-autoconfigure/3.5.5/mybatis-plus-spring-boot-autoconfigure-3.5.5.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-boot-starter/1.4.12/mybatis-plus-join-boot-starter-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-extension/1.4.12/mybatis-plus-join-extension-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-core/1.4.12/mybatis-plus-join-core-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-annotation/1.4.12/mybatis-plus-join-annotation-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v33x/1.4.12/mybatis-plus-join-adapter-v33x-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-base/1.4.12/mybatis-plus-join-adapter-base-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-jsqlparser/1.4.12/mybatis-plus-join-adapter-jsqlparser-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-jsqlparser-v46/1.4.12/mybatis-plus-join-adapter-jsqlparser-v46-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v3431/1.4.12/mybatis-plus-join-adapter-v3431-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v352/1.4.12/mybatis-plus-join-adapter-v352-1.4.12.jar:/Users/<USER>/.m2/repository/com/github/yulichang/mybatis-plus-join-adapter-v355/1.4.12/mybatis-plus-join-adapter-v355-1.4.12.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-aop/3.5.3/spring-boot-starter-aop-3.5.3.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.24/aspectjweaver-1.9.24.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.38/lombok-1.18.38.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-all/5.8.26/hutool-all-5.8.26.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.17.0/commons-lang3-3.17.0.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.5.18/logback-classic-1.5.18.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.5.18/logback-core-1.5.18.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/31.1-jre/guava-31.1-jre.jar:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.12.0/checker-qual-3.12.0.jar:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.11.0/error_prone_annotations-2.11.0.jar:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/1.3/j2objc-annotations-1.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-properties/2.19.1/jackson-dataformat-properties-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.19.1/jackson-core-2.19.1.jar:/Users/<USER>/.m2/repository/com/dameng/DmJdbcDriver18/8.1.2.79/DmJdbcDriver18-8.1.2.79.jar:/Users/<USER>/.m2/repository/com/pig4cloud/excel/excel-spring-boot-starter/3.2.1/excel-spring-boot-starter-3.2.1.jar:/Users/<USER>/.m2/repository/com/alibaba/easyexcel/3.3.4/easyexcel-3.3.4.jar:/Users/<USER>/.m2/repository/com/alibaba/easyexcel-core/3.3.4/easyexcel-core-3.3.4.jar:/Users/<USER>/.m2/repository/com/alibaba/easyexcel-support/3.3.4/easyexcel-support-3.3.4.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi/4.1.2/poi-4.1.2.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.4/commons-collections4-4.4.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-math3/3.6.1/commons-math3-3.6.1.jar:/Users/<USER>/.m2/repository/com/zaxxer/SparseBitSet/1.2/SparseBitSet-1.2.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/4.1.2/poi-ooxml-4.1.2.jar:/Users/<USER>/.m2/repository/com/github/virtuald/curvesapi/1.06/curvesapi-1.06.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-csv/1.8/commons-csv-1.8.jar:/Users/<USER>/.m2/repository/org/ehcache/ehcache/3.10.8/ehcache-3.10.8.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.26.1/commons-compress-1.26.1.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.18.0/commons-codec-1.18.0.jar:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/3.0/hamcrest-core-3.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-test/3.5.3/spring-boot-starter-test-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/3.5.3/spring-boot-test-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test-autoconfigure/3.5.3/spring-boot-test-autoconfigure-3.5.3.jar:/Users/<USER>/.m2/repository/com/jayway/jsonpath/json-path/2.9.0/json-path-2.9.0.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/4.0.2/jakarta.xml.bind-api-4.0.2.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/2.1.3/jakarta.activation-api-2.1.3.jar:/Users/<USER>/.m2/repository/net/minidev/json-smart/2.5.2/json-smart-2.5.2.jar:/Users/<USER>/.m2/repository/net/minidev/accessors-smart/2.5.2/accessors-smart-2.5.2.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.27.3/assertj-core-3.27.3.jar:/Users/<USER>/.m2/repository/org/awaitility/awaitility/4.2.2/awaitility-4.2.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/3.0/hamcrest-3.0.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter/5.12.2/junit-jupiter-5.12.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-api/5.12.2/junit-jupiter-api-5.12.2.jar:/Users/<USER>/.m2/repository/org/opentest4j/opentest4j/1.3.0/opentest4j-1.3.0.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-commons/1.12.2/junit-platform-commons-1.12.2.jar:/Users/<USER>/.m2/repository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-params/5.12.2/junit-jupiter-params-5.12.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-engine/5.12.2/junit-jupiter-engine-5.12.2.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-engine/1.12.2/junit-platform-engine-1.12.2.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-core/5.17.0/mockito-core-5.17.0.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.17.6/byte-buddy-agent-1.17.6.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.3/objenesis-3.3.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-junit-jupiter/5.17.0/mockito-junit-jupiter-5.17.0.jar:/Users/<USER>/.m2/repository/org/skyscreamer/jsonassert/1.5.3/jsonassert-1.5.3.jar:/Users/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-test/6.2.8/spring-test-6.2.8.jar:/Users/<USER>/.m2/repository/org/xmlunit/xmlunit-core/2.10.2/xmlunit-core-2.10.2.jar:/Users/<USER>/.m2/repository/com/github/whvcse/easy-captcha/1.6.2/easy-captcha-1.6.2.jar:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk18on/1.76/bcprov-jdk18on-1.76.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-impl/0.11.2/jjwt-impl-0.11.2.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-api/0.11.2/jjwt-api-0.11.2.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-jackson/0.11.2/jjwt-jackson-0.11.2.jar:/Users/<USER>/.m2/repository/cglib/cglib/3.3.0/cglib-3.3.0.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm/7.1/asm-7.1.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-text/1.13.1/commons-text-1.13.1.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.16.1/commons-io-2.16.1.jar:/Users/<USER>/.m2/repository/org/codehaus/janino/janino/3.1.12/janino-3.1.12.jar:/Users/<USER>/.m2/repository/org/codehaus/janino/commons-compiler/3.1.12/commons-compiler-3.1.12.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-websocket/3.5.3/spring-boot-starter-websocket-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-messaging/6.2.8/spring-messaging-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-websocket/6.2.8/spring-websocket-6.2.8.jar:/Users/<USER>/.m2/repository/org/java-websocket/Java-WebSocket/1.5.2/Java-WebSocket-1.5.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-rsocket/3.5.3/spring-boot-starter-rsocket-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-reactor-netty/3.5.3/spring-boot-starter-reactor-netty-3.5.3.jar:/Users/<USER>/.m2/repository/io/projectreactor/netty/reactor-netty-http/1.2.7/reactor-netty-http-1.2.7.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-http/4.1.122.Final/netty-codec-http-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-http2/4.1.122.Final/netty-codec-http2-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns-native-macos/4.1.122.Final/netty-resolver-dns-native-macos-4.1.122.Final-osx-x86_64.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns-classes-macos/4.1.122.Final/netty-resolver-dns-classes-macos-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-epoll/4.1.122.Final/netty-transport-native-epoll-4.1.122.Final-linux-x86_64.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-classes-epoll/4.1.122.Final/netty-transport-classes-epoll-4.1.122.Final.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-cbor/2.19.1/jackson-dataformat-cbor-2.19.1.jar:/Users/<USER>/.m2/repository/io/rsocket/rsocket-core/1.1.5/rsocket-core-1.1.5.jar:/Users/<USER>/.m2/repository/io/rsocket/rsocket-transport-netty/1.1.5/rsocket-transport-netty-1.1.5.jar:/Users/<USER>/.m2/repository/io/projectreactor/netty/reactor-netty-core/1.2.7/reactor-netty-core-1.2.7.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler-proxy/4.1.122.Final/netty-handler-proxy-4.1.122.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-socks/4.1.122.Final/netty-codec-socks-4.1.122.Final.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-thymeleaf/3.5.3/spring-boot-starter-thymeleaf-3.5.3.jar:/Users/<USER>/.m2/repository/org/thymeleaf/thymeleaf-spring6/3.1.3.RELEASE/thymeleaf-spring6-3.1.3.RELEASE.jar:/Users/<USER>/.m2/repository/org/thymeleaf/thymeleaf/3.1.3.RELEASE/thymeleaf-3.1.3.RELEASE.jar:/Users/<USER>/.m2/repository/org/attoparser/attoparser/2.0.7.RELEASE/attoparser-2.0.7.RELEASE.jar:/Users/<USER>/.m2/repository/org/unbescape/unbescape/1.1.6.RELEASE/unbescape-1.1.6.RELEASE.jar:/Users/<USER>/.m2/repository/com/payne/payne-tools/1.0.1/payne-tools-1.0.1.jar:/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes:/Users/<USER>/.m2/repository/com/sun/mail/javax.mail/1.6.2/javax.mail-1.6.2.jar:/Users/<USER>/.m2/repository/javax/activation/activation/1.1/activation-1.1.jar:/Users/<USER>/project/ele-admin/dev-platform/payne-generator/target/classes:/Users/<USER>/.m2/repository/org/apache/velocity/velocity-engine-core/2.3/velocity-engine-core-2.3.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar:/Users/<USER>/.m2/repository/com/oracle/ojdbc8/********.0/ojdbc8-********.0.jar:/Users/<USER>/.m2/repository/com/mysql/mysql-connector-j/8.0.33/mysql-connector-j-8.0.33.jar:/Users/<USER>/.m2/repository/com/google/protobuf/protobuf-java/3.21.9/protobuf-java-3.21.9.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/6.3.0/HikariCP-6.3.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-devtools/3.5.3/spring-boot-devtools-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/3.5.3/spring-boot-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/6.2.8/spring-core-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/6.2.8/spring-jcl-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/6.2.8/spring-context-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/3.5.3/spring-boot-autoconfigure-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/3.5.3/spring-boot-starter-web-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/3.5.3/spring-boot-starter-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/3.5.3/spring-boot-starter-logging-3.5.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.24.3/log4j-to-slf4j-2.24.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.24.3/log4j-api-2.24.3.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/2.0.17/jul-to-slf4j-2.0.17.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/2.1/snakeyaml-2.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/3.5.3/spring-boot-starter-json-3.5.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.19.1/jackson-databind-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.19.1/jackson-annotations-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.19.1/jackson-datatype-jdk8-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.19.1/jackson-datatype-jsr310-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.19.1/jackson-module-parameter-names-2.19.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/3.5.3/spring-boot-starter-tomcat-3.5.3.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/10.1.42/tomcat-embed-core-10.1.42.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/10.1.42/tomcat-embed-el-10.1.42.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/10.1.42/tomcat-embed-websocket-10.1.42.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/6.2.8/spring-web-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/6.2.8/spring-beans-6.2.8.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.15.1/micrometer-observation-1.15.1.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.15.1/micrometer-commons-1.15.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/6.2.8/spring-webmvc-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/6.2.8/spring-aop-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/6.2.8/spring-expression-6.2.8.jar:/Users/<USER>/Applications/IntelliJ IDEA Ultimate.app/Contents/lib/idea_rt.jar, java.vm.vendor=Azul Systems, Inc., sun.java.launcher=SUN_STANDARD, user.country=CN, sun.cpu.endian=little, user.language=zh, https.proxyHost=127.0.0.1, ftp.nonProxyHosts=127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|**********/10|*.**********/10|localhost|*.localhost|local|*.local|e.crashlytics.com|*.e.crashlytics.com|captive.apple.com|*.captive.apple.com|::ffff:0:0:0:0/1|*.::ffff:0:0:0:0/1|::ffff:128:0:0:0/1|*.::ffff:128:0:0:0/1|***************/32|*.***************/32, spring.jmx.enabled=true, rebel.env.ide.plugin.version=2025.3.1, java.runtime.name=OpenJDK Runtime Environment, rebel.native.image=/private/var/folders/q5/104n2ctj1m19xf01gllpj3240000gn/T/jrebel-JRebel-202508011409/lib/libjrebel64.dylib, rebel.env.ide=intellij, rebel.plugins=/Users/<USER>/Library/Application Support/JetBrains/IntelliJIdea2025.2/plugins/jr-mp-ide-idea/lib/jr-mybatisplus-1.0.7.jar, java.vendor.url.bug=http://www.azul.com/support/, user.dir=/Users/<USER>/project/ele-admin, java.vm.version=17.0.15+6-LTS, rebel.base=/Users/<USER>/.jrebel, rebel.env.ide.product=IU}. 
[2m2025-08-06 23:23:19.485[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS, DESCRIPTION FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-08-06 23:23:19.485[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-08-06 23:23:19.500[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-06 23:23:19.529[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-08-06 23:23:19.548[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-08-06 23:23:19.566[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-08-06 23:23:19.567[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-08-06 23:23:19.567[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-08-06 23:23:19.574[0;39m [32mDEBUG[0;39m [35m44647[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-08-06 23:38:06.870[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[nexc.home:27017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Exception in monitor thread while connecting to server paynexc.home:27017

com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.internal.connection.InternalStreamConnection.createReadTimeoutException(InternalStreamConnection.java:819)
	at com.mongodb.internal.connection.InternalStreamConnection.translateReadException(InternalStreamConnection.java:807)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:857)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:517)
	at com.mongodb.internal.connection.InternalStreamConnection.receive(InternalStreamConnection.java:469)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitor.lookupServerDescription(DefaultServerMonitor.java:249)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitor.run(DefaultServerMonitor.java:176)
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.base/sun.nio.ch.NioSocketImpl.timedRead(NioSocketImpl.java:288)
	at java.base/sun.nio.ch.NioSocketImpl.implRead(NioSocketImpl.java:314)
	at java.base/sun.nio.ch.NioSocketImpl.read(NioSocketImpl.java:355)
	at java.base/sun.nio.ch.NioSocketImpl$1.read(NioSocketImpl.java:808)
	at java.base/java.net.Socket$SocketInputStream.read(Socket.java:976)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:182)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:824)
	... 4 common frames omitted

[2m2025-08-06 23:38:07.083[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[nexc.home:27017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Monitor thread successfully connected to server with description ServerDescription{address=paynexc.home:27017, type=STANDALONE, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=21, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=197427542, minRoundTripTimeNanos=0}
[2m2025-08-06 23:38:27.334[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[ionShutdownHook][0;39m [36mo.s.b.w.e.tomcat.GracefulShutdown       [0;39m [2m:[0;39m Commencing graceful shutdown. Waiting for active requests to complete
[2m2025-08-06 23:38:27.350[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[tomcat-shutdown][0;39m [36mo.s.b.w.e.tomcat.GracefulShutdown       [0;39m [2m:[0;39m Graceful shutdown complete
[2m2025-08-06 23:38:27.384[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[ionShutdownHook][0;39m [36mj.LocalContainerEntityManagerFactoryBean[0;39m [2m:[0;39m Closing JPA EntityManagerFactory for persistence unit 'default'
[2m2025-08-06 23:38:27.413[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[ionShutdownHook][0;39m [36mcom.alibaba.druid.pool.DruidDataSource  [0;39m [2m:[0;39m {dataSource-1} closing ...
[2m2025-08-06 23:38:27.423[0;39m [32m INFO[0;39m [35m44647[0;39m [2m---[0;39m [2m[ionShutdownHook][0;39m [36mcom.alibaba.druid.pool.DruidDataSource  [0;39m [2m:[0;39m {dataSource-1} closed
